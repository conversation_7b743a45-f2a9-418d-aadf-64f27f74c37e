<?xml version="1.0" encoding="utf-8" ?>

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN" "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"><head><title>Setting up the software</title><link rel="stylesheet" href="epub.css" type="text/css"/><meta name="generator" content="DocBook XSL Stylesheets V1.75.2"/></head><body id="page"><div class="section" title="Setting up the software"><div class="titlepage"><div><div><h1 class="title"><a id="ch10lvl1sec57"/>Setting up the software</h1></div></div></div><p>Now, it's<a id="id475" class="indexterm"/> time to set up the software to manage our infrared detector, and to do it, we're going to use the <a id="id476" class="indexterm"/>
<span class="strong"><strong>LIRC</strong></span> (<span class="strong"><strong>Linux Infrared Remote Control</strong></span>) subsystem, which is a special code that has been developed for this purpose.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note106"/>Note</h3><p>For further information<a id="id477" class="indexterm"/> on the LIRC subsystem, you can take a look at <a class="ulink" href="http://www.lirc.org/">http://www.lirc.org/</a>.</p></div></div><p>We'll need a kernel driver to convert the pulse generated by the infrared detector into well-defined messages, and then to send them, through a LIRC device, to the userspace programs. At userspace level, we're going to use a special tool from the LIRC project in order to convert the infrared messages into input events, that is, the messages that a normal<a id="id478" class="indexterm"/> keyboard sends to the kernel.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note107"/>Note</h3><p>For further information on the <a id="id479" class="indexterm"/>Linux input subsystem, you can take a look at <a class="ulink" href="https://www.kernel.org/doc/Documentation/input/input.txt">https://www.kernel.org/doc/Documentation/input/input.txt</a>.</p></div></div><div class="section" title="Setting up the kernel driver"><div class="titlepage"><div><div><h2 class="title"><a id="ch10lvl2sec84"/>Setting up the kernel driver</h2></div></div></div><p>To <a id="id480" class="indexterm"/>set up the kernel driver to manage our infrared receiver, we can use a procedure similar to the one used in <a class="link" href="ch04.html" title="Chapter&#xA0;4.&#xA0;Google Docs Weather Station">Chapter 4</a>, <span class="emphasis"><em>Google Docs Weather Station</em></span>. Once the sources from the GitHub repository are downloaded, we have to follow the procedure in <a class="link" href="ch04.html" title="Chapter&#xA0;4.&#xA0;Google Docs Weather Station">Chapter 4</a>, <span class="emphasis"><em>Google Docs Weather Station</em></span>, until the step where we have to apply our special patch. In fact, in this case, we have to apply the patch in the <code class="literal">chapter_10/0001-Add-support-for-Homebrew-GPIO-Port-Receiver-Transmit.patch</code> file in the book's example code repository in order to add the infrared receiver support.</p><p>The command is as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>$ git am --whitespace=nowarn 0001-Add-support-for-Homebrew-GPIO-Port-Receiver-Transmit.patch</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip127"/>Tip</h3><p>Note that the <code class="literal">--whitespace=nowarn</code> command-line option is needed just in case your <code class="literal">git</code> system is configured to automatically fix up the whitespace errors, which is wrong in this case.</p></div></div><p>If everything works well, the <code class="literal">git log</code> command should display the following:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>$ git log -1</strong></span>
<span class="strong"><strong>commit be816108417ce82c7114ebd578ac32a45aef934a</strong></span>
<span class="strong"><strong>Author:     Rodolfo Giometti &lt;<EMAIL>&gt;</strong></span>
<span class="strong"><strong>AuthorDate: Sun Oct 11 08:43:49 2015 +0200</strong></span>
<span class="strong"><strong>Commit:     Rodolfo Giometti &lt;<EMAIL>&gt;</strong></span>
<span class="strong"><strong>CommitDate: Thu Oct 22 14:53:44 2015 +0200</strong></span>

<span class="strong"><strong>    Add support for Homebrew GPIO Port Receiver/Transmitter</strong></span>
<span class="strong"><strong>    </strong></span>
<span class="strong"><strong>    Signed-off-by: Rodolfo Giometti &lt;<EMAIL>&gt;</strong></span>
</pre></div><p>Before starting the kernel compilation, let me spend a few words regarding this patch. It simply adds a new driver into the <code class="literal">KERNEL/drivers/staging/media/lirc</code> directory of the Linux sources, so, after applying the patch, if we take a look at the new file <code class="literal">lirc_gpio.c</code>, we can discover how it works.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip128"/>Tip</h3><p>The following is a brief explanation of the driver code. If you don't care about it, and just wish to use the driver <span class="emphasis"><em>as-is</em></span>, you can safely skip this part until the kernel compilation commands.</p></div></div><p>At<a id="id481" class="indexterm"/> the beginning, we have the kernel module parameters as follows:</p><div class="informalexample"><pre class="programlisting">/*
 * Module parameters
 */

/* Set the default GPIO input pin */
staticintgpio_in = -1;
MODULE_PARM_DESC(gpio_in, "GPIO input/receiver pin number "
                   "(warning: it MUST be an interrupt capable pin!)");
module_param(gpio_in, int, S_IRUGO);

/* Set the default GPIO output pin */
staticintgpio_out = -1;
MODULE_PARM_DESC(gpio_out, "GPIO output/transmitter pin number");
module_param(gpio_out, int, S_IRUGO);

/* Set the sense mode: -1 = auto, 0 = active high, 1 = active low */
staticint sense = -1;
MODULE_PARM_DESC(sense, "Override autodetection of IR receiver circuit: ""0 = active high, 1 = active low (default -1 = auto)");
module_param(sense, int, S_IRUGO);

/* Use softcarrier by default */
static unsigned int softcarrier = 1;
MODULE_PARM_DESC(softcarrier, "Software carrier: 0 = off, 1 = on (default on)");
module_param(softcarrier, uint, S_IRUGO);</pre></div><p>We're going to use the <code class="literal">gpio_in</code> parameter in order to specify the input pin which the infrared receiver is connected to. Then, some local functions follow (which I don't report here), and then we find the file's operations definitions:</p><div class="informalexample"><pre class="programlisting">static const struct file_operationslirc_fops = {
        .owner          = THIS_MODULE,
        .write          = lirc_write,
        .unlocked_ioctl = lirc_ioctl,
        .read           = lirc_dev_fop_read,
        .poll           = lirc_dev_fop_poll,
        .open           = lirc_dev_fop_open,
        .release        = lirc_dev_fop_close,
        .llseek         = no_llseek,
};</pre></div><p>Each <a id="id482" class="indexterm"/>function is related to a well-defined system call that we can use on the new LIRC device.</p><p>At the very bottom of the file, there is the <code class="literal">lirc_gpio_init_module()</code> function, which is responsible for setting up the new device. As a first step, this function tries to request all needed GPIO lines:</p><div class="informalexample"><pre class="programlisting"> /*  
   * Check for valid gpio pin numbers 
   */ 
   ret = gpio_request(gpio_in, LIRC_GPIO_NAME " ir/in"); 
   if (ret) { 
      pr_err("failed to request GPIO %u\n", gpio_in); 
      return -EINVAL; 
   } 
   ret = gpio_direction_input(gpio_in); 
   if (ret) { 
      pr_err("failed to set pin direction for gpio_in\n"); 
      ret = -EINVAL; 
      goto exit_free_gpio_in; 
   } 
   pr_info("got GPIO %d for receiving\n", gpio_in); 
   /* Is GPIO in pin IRQ capable? */ 
   irq = gpio_to_irq(gpio_in); 
   if (irq &lt; 0) { 
      pr_err("failed to map GPIO %d to IRQ\n", gpio_in); 
      ret = -EINVAL;
      goto exit_free_gpio_in; 
   } 
   ret = request_irq(irq, (irq_handler_t) irq_handler, IRQF_TRIGGER_FALLING | IRQF_TRIGGER_ RISING, LIRC_GPIO_NAME, (void *) 0); 
   if (ret &lt; 0) { 
      pr_err("unable to request IRQ %d\n", irq); 
      goto exit_free_gpio_in; 
   } 
   pr_info("got IRQ %d for GPIO %d\n", irq, gpio_in); 
   if (gpio_out &gt;= 0) { 
      ret = gpio_request(gpio_out, LIRC_GPIO_NAME " ir/ out"); 
      if (ret) { 
         pr_err("failed to request GPIO %u\n", gpio_ out); 
         goto exit_free_irq; 
      } 
      ret = gpio_direction_output(gpio_out, 0); 
      if (ret) { 
         pr_err("failed to set pin direction for gpio_ out\n"); 
         ret = -EINVAL; 
         goto exit_free_gpio_out; 
      } 
      pr_info("got GPIO %d for transmitting\n", gpio_out); 
   } </pre></div><p>After <a id="id483" class="indexterm"/>requesting the <code class="literal">gpio_in</code> pin, the function sets it up as an input pin and then checks if such a GPIO line is interrupt-capable; otherwise, the driver can't work properly. If so, the function requests the IRQ line, and then it proceeds with the <code class="literal">gpio_out</code> pin (note that it's not mandatory).</p><p>Then, the driver sets the sense mode by using a little auto-detect procedure (if not directly specified by the user at loading time), as shown in the following code snippet:</p><div class="informalexample"><pre class="programlisting">     /* Set the sense mode */
     if (sense != -1) {
             pr_info("manually using active %s receiver on GPIO %d\n",
                     sense ? "low" : "high", gpio_in);
     } else {
             /* wait 1/2 sec for the power supply */
             msleep(500);

             /*
              * probe 9 times every 0.04s, collect "votes" for
              * active high/low
              */
             nlow = 0;
             nhigh = 0;
             for (i = 0; i &lt; 9; i++) {
                     if (gpio_get_value(gpio_in))
                             nlow++;
                     else
                             nhigh++;
                     msleep(40);
             }
             sense = (nlow &gt;= nhigh ? 1 : 0);
             pr_info("auto-detected active %s receiver on GPIO pin %d\n",
                     sense ? "low" : "high", gpio_in);
     }</pre></div><p>Then, <a id="id484" class="indexterm"/>we can finally set up the LIRC driver by calling first the <code class="literal">lirc_buffer_init()</code> function, to properly allocate a memory buffer for the messages management, and then by calling the <code class="literal">lirc_register_driver()</code>, to register the driver into the system, as shown in the following code snippet:</p><div class="informalexample"><pre class="programlisting">     /*
      * Setup the LIRC driver
      */

     ret = lirc_buffer_init(&amp;rbuf, sizeof(int), RBUF_LEN);
     if (ret &lt; 0) {
          pr_err("unable to init lirc buffer!\n");
             ret = -ENOMEM;
             goto exit_free_gpio_out;
     }

     ret = platform_driver_register(&amp;lirc_gpio_driver);
     if (ret) {
             pr_err("error in lirc register\n");
             goto exit_free_buffer;
        }

        lirc_gpio_dev = platform_device_alloc(LIRC_GPIO_NAME, 0);
        if (!lirc_gpio_dev) {
                pr_err("error on platform device alloc!\n");
                ret = -ENOMEM;
goto exit_driver_unreg;
        }

        ret = platform_device_add(lirc_gpio_dev);
        if (ret) {
                pr_err("error on platform device add!\n");
goto exit_device_put;
        }

        driver.features = LIRC_CAN_REC_MODE2;
        if (gpio_out &gt;= 0) {
                driver.features |= LIRC_CAN_SET_SEND_DUTY_CYCLE |
                          LIRC_CAN_SET_SEND_CARRIER |
                          LIRC_CAN_SEND_PULSE;
        }

        driver.dev = &amp;lirc_gpio_dev-&gt;dev;
        driver.minor = lirc_register_driver(&amp;driver);

        if (driver.minor &lt; 0) {
                pr_err("device registration failed!");
                ret = -EIO;
goto exit_device_put;
        }

        pr_info("driver registered!\n");

        return 0;</pre></div><p>Ok, now we can start to compile the kernel with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>$ ./build_kernel.sh</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip129"/>Tip</h3><p>This step, and the subsequent one, are time consuming and require patience, so you should take a cup of your preferred tea or coffee, and just wait.</p></div></div><p>After <a id="id485" class="indexterm"/>some time, the procedure will present the standard kernel configuration panel, and now we should verify that the needed drivers are enabled. You should navigate in the menu to <span class="strong"><strong>Device Drivers</strong></span> | <span class="strong"><strong>Staging drivers</strong></span> | <span class="strong"><strong>Media staging drivers</strong></span> | <span class="strong"><strong>Linux Infrared Remote Control IR receiver/transmitter drivers</strong></span>, where the <span class="strong"><strong>Homebrew GPIO Port Receiver/Transmitter</strong></span> entry should be selected as module (<span class="strong"><strong>&lt;M&gt;</strong></span>).</p><p>Then, exit the configuration menu and the kernel compilation should start. Then, when it ends, the new kernel image will be ready, and the following message should appear:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>-----------------------------</strong></span>
<span class="strong"><strong>Script Complete</strong></span>
<span class="strong"><strong>eewiki.net: [user@localhost:~$ export kernel_version=3.13.11-bone12]</strong></span>
<span class="strong"><strong>-----------------------------</strong></span>
</pre></div><p>Now, we can install it on the microSD, using the following installation tool:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>$ ./tools/install_kernel.sh</strong></span>
</pre></div><p>If everything works well, after the usual login, we can verify that the new kernel is really running using the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# uname -a</strong></span>
<span class="strong"><strong>Linux arm 3.13.11-bone12 #1 SMP Sun Oct 11 09:15:46 CEST 2015 armv7l GNU/Linux</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip130"/>Tip</h3><p>Note that the kernel version on your system may be more recent than mine.</p></div></div><p>Okay, the <a id="id486" class="indexterm"/>new kernel is ready! Now, we can load the LIRC driver by using the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# modprobe lirc_gpio gpio_in=60</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip131"/>Tip</h3><p>Note that the GPIO 60 must not be in use, or you may get an error like the following:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>ERROR: could not insert 'lirc_gpio': Invalid argument</strong></span>
</pre></div></div></div><p>The kernel messages should look like the following:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>lirc_dev: IR Remote Control driver registered, major 241</strong></span>
<span class="strong"><strong>lirc_gpio: module is from the staging directory, the quality is unknown, you have been warned.</strong></span>
<span class="strong"><strong>lirc_gpio: got GPIO 60 for receiving</strong></span>
<span class="strong"><strong>lirc_gpio: got IRQ 204 for GPIO 60</strong></span>
<span class="strong"><strong>lirc_gpio: auto-detected active low receiver on GPIO pin 60</strong></span>
<span class="strong"><strong>lirc_gpio lirc_gpio.0: lirc_dev: driver lirc_gpio registered at minor = 0</strong></span>
<span class="strong"><strong>lirc_gpio: driver registered!</strong></span>
</pre></div><p>Also, a new entry should now be ready under the <code class="literal">/dev</code> directory:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~/chapter_10# ls -l /dev/lirc*</strong></span>
<span class="strong"><strong>crw-rw---T 1 root video 241, 0 Aug 13 16:35 /dev/lirc0</strong></span>
</pre></div></div><div class="section" title="The LIRC tools"><div class="titlepage"><div><div><h2 class="title"><a id="ch10lvl2sec85"/>The LIRC tools</h2></div></div></div><p>Now <a id="id487" class="indexterm"/>that the kernel module is set up and running, we need some userspace tools to manage it. So, let's install the <code class="literal">lirc</code> package with the usual <code class="literal">aptitude</code> command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# aptitude install lirc</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Setting up lirc (0.9.0~pre1-1) ...</strong></span>
<span class="strong"><strong>[ ok ] No valid /etc/lirc/lircd.conf has been found..</strong></span>
<span class="strong"><strong>[ ok ] Remote control support has been disabled..</strong></span>
<span class="strong"><strong>[ ok ] Reconfigure LIRC or manually replace /etc/lirc/lircd.conf to enable..</strong></span>
</pre></div><p>As stated by the preceding line, to enable the <code class="literal">lircd</code> daemon (that is, the tools we need), we have to replace the configuration file <code class="literal">/etc/lirc/lircd.conf</code>; however, we're not going to use the daemon this way. In reality, we can test that the driver is really working as expected by executing the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# mode2 --driver default --device /dev/lirc0</strong></span>
</pre></div><p>Nothing should happen until you point your remote controller at the infrared receiver and press a button. In this case, you should see some output, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>space 3333126</strong></span>
<span class="strong"><strong>pulse 8985</strong></span>
<span class="strong"><strong>space 4503</strong></span>
<span class="strong"><strong>pulse 564</strong></span>
<span class="strong"><strong>space 535</strong></span>
<span class="strong"><strong>pulse 564</strong></span>
<span class="strong"><strong>space 561</strong></span>
<span class="strong"><strong>pulse 542</strong></span>
<span class="strong"><strong>space 551</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div><p>Okay! The <code class="literal">/dev/lirc0</code> device is functioning, and the driver correctly detects the messages from the remote controller! Now, we have to create a custom configuration file in order to associate an input event to each of the remote controller's buttons.</p><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip132"/>Tip</h3><p>Again, due to lack of space, I'm going to configure just a few buttons in the following example; but you can add whatever you want.</p></div></div><p>The command to use is <code class="literal">irrecord</code>, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# irrecord --driver default --device /dev/lirc0 myremote.conf</strong></span>
</pre></div><a id="id488" class="indexterm"/><p>
<code class="literal">myremote.conf</code> is the file where we wish to save our configuration. The program then will show an output as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>irrecord -  application for recording IR-codes for usage with lirc</strong></span>

<span class="strong"><strong>Copyright (C) 1998,1999 Christoph Bartelmus(<EMAIL>)</strong></span>

<span class="strong"><strong>This program will record the signals from your remote control</strong></span>
<span class="strong"><strong>and create a config file for lircd.</strong></span>


<span class="strong"><strong>A proper config file for lircd is maybe the most vital part of this</strong></span>
<span class="strong"><strong>package, so you should invest some time to create a working config</strong></span>
<span class="strong"><strong>file. Although I put a good deal of effort in this program it is often</strong></span>
<span class="strong"><strong>notpossible to automatically recognize all features of a remote</strong></span>
<span class="strong"><strong>control. Often short-comings of the receiver hardware make it nearly</strong></span>
<span class="strong"><strong>impossible. If you have problems to create a config file READ THE</strong></span>
<span class="strong"><strong>DOCUMENTATION of this package, especially section "Adding new remote</strong></span>
<span class="strong"><strong>controls" for how to get help.</strong></span>

<span class="strong"><strong>If there already is a remote control of the same brand available at</strong></span>
<span class="strong"><strong>http://www.lirc.org/remotes/ you might also want to try using such a</strong></span>
<span class="strong"><strong>remote as a template. The config files already contain all</strong></span>
<span class="strong"><strong>parameters of the protocol used by remotes of a certain brand and</strong></span>
<span class="strong"><strong>knowing these parameters makes the job of this program much</strong></span>
<span class="strong"><strong>easier. There are also template files for the most common protocols</strong></span>
<span class="strong"><strong>available in the remotes/generic/ directory of the source</strong></span>
<span class="strong"><strong>distribution of this package. You can use a template files by</strong></span>
<span class="strong"><strong>providing the path of the file as command line parameter.</strong></span>

<span class="strong"><strong>Please send the finished config files to &lt;<EMAIL>&gt; so that I</strong></span>
<span class="strong"><strong>can make them available to others. Don't forget to put all information</strong></span>
<span class="strong"><strong>that you can get about the remote control in the header of the file.</strong></span>

<span class="strong"><strong>Press RETURN to continue.</strong></span>
</pre></div><p>Okay, let's press the <span class="emphasis"><em>return</em></span>/<span class="emphasis"><em>Enter</em></span> key, and the program will continue showing the following message:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Now start pressing buttons on your remote control.</strong></span>

<span class="strong"><strong>It is very important that you press many different buttons and hold them</strong></span>
<span class="strong"><strong>down for approximately one second. Each button should generate at least one dot but in no case more than ten dots of output.</strong></span>
<span class="strong"><strong>Don't stop pressing buttons until two lines of dots (2x80) have been</strong></span>
<span class="strong"><strong>generated.</strong></span>

<span class="strong"><strong>Press RETURN now to start recording.</strong></span>
</pre></div><p>Ok, now<a id="id489" class="indexterm"/> it's really important to carefully follow the preceding instructions. So, let's start pressing different buttons and hold them for approximately one second in such a way as to generate at least one dot, but in no case more than ten dots, of output for each press!</p><p>So, the program will start printing the dots until it reaches the end of the terminal as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>....................................................................</strong></span>
<span class="strong"><strong>Found const length: 107736</strong></span>
</pre></div><p>When the first line is finished, the program will display the following message, and new dots will appear, but, this time, just one per pressed button!:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Please keep on pressing buttons like described above.</strong></span>
<span class="strong"><strong>............irrecord: signal too long</strong></span>
<span class="strong"><strong>Creating config file in raw mode.</strong></span>
<span class="strong"><strong>Now enter the names for the buttons.</strong></span>
</pre></div><p>Now, the first detection stage is finished, and we can start the real detection one button at a time. Now, the system will ask for a button name or the <span class="emphasis"><em>Enter</em></span> key to finish:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Please enter the name for the next button (press &lt;ENTER&gt; to finish recording)</strong></span>
</pre></div><p>Now, I enter the name of button <span class="strong"><strong>0</strong></span> by inserting the <code class="literal">KEY_0</code> string, as follows. Then, the system will ask you to hold down the button <span class="strong"><strong>0</strong></span> until it has got it:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>KEY_0</strong></span>

<span class="strong"><strong>Now hold down button "KEY_0".</strong></span>
<span class="strong"><strong>Got it.</strong></span>
<span class="strong"><strong>Signal length is 67</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip133"/>Tip</h3><p>The valid button names can be listed by using the <code class="literal">irrecord</code> command, as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# irrecord --list-namespace</strong></span>
<span class="strong"><strong>KEY_0</strong></span>
<span class="strong"><strong>KEY_102ND</strong></span>
<span class="strong"><strong>KEY_1</strong></span>
<span class="strong"><strong>KEY_2</strong></span>
<span class="strong"><strong>KEY_3</strong></span>
<span class="strong"><strong>KEY_4</strong></span>
<span class="strong"><strong>KEY_5</strong></span>
<span class="strong"><strong>KEY_6</strong></span>
<span class="strong"><strong>KEY_7</strong></span>
<span class="strong"><strong>KEY_8</strong></span>
<span class="strong"><strong>KEY_9</strong></span>
<span class="strong"><strong>KEY_A</strong></span>
<span class="strong"><strong>KEY_AB</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div></div></div><p>Then, the<a id="id490" class="indexterm"/> procedure restarts for the next buttons as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Please enter the name for the next button (press &lt;ENTER&gt; to finish recording)</strong></span>
<span class="strong"><strong>KEY_1</strong></span>

<span class="strong"><strong>Now hold down button "KEY_1".</strong></span>
<span class="strong"><strong>Got it.</strong></span>
<span class="strong"><strong>Signal length is 67</strong></span>

<span class="strong"><strong>Please enter the name for the next button (press &lt;ENTER&gt; to finish recording)</strong></span>
<span class="strong"><strong>KEY_2</strong></span>

<span class="strong"><strong>Now hold down button "KEY_2".</strong></span>
<span class="strong"><strong>Got it.</strong></span>
<span class="strong"><strong>Signal length is 67</strong></span>

<span class="strong"><strong>Please enter the name for the next button (press &lt;ENTER&gt; to finish recording)</strong></span>
<span class="strong"><strong>KEY_3</strong></span>

<span class="strong"><strong>Now hold down button "KEY_3".</strong></span>
<span class="strong"><strong>Got it.</strong></span>
<span class="strong"><strong>Signal length is 67</strong></span>
</pre></div><p>At this point, I simply enter no names and just press <span class="emphasis"><em>Enter</em></span> to exit, and then I get the prompt again:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Please enter the name for the next button (press &lt;ENTER&gt; to finish recording)</strong></span>

<span class="strong"><strong>root@arm:~#</strong></span>
</pre></div><p>Now, a <a id="id491" class="indexterm"/>new file called <code class="literal">myremote.conf</code> should be ready. The following is a snippet of my file:</p><div class="informalexample"><pre class="programlisting"># Please make this file available to others
# by sending it to &lt;<EMAIL>&gt;
#
# this config file was automatically generated
# using lirc-0.9.0-pre1(default) on Wed Aug 13 15:54:26 2014
#
# contributed by
#
# brand:                       myremote.conf
# model no. of remote control:
# devices being controlled by this remote:
#

begin remote

  name  myremote.conf
  flags RAW_CODES
  eps            30
  aeps          100

  gap          96036

begin raw_codes

          name KEY_0
             8998    4478     566     541     570     541
              570     541     570     542     570     541
              570     541     570     541     578     533
              570     541     570     541     570     542
              570     540     570    1679     571     541
              570     541     569     543     569     542
              570     541     570    1679     570    1678
              571     541     570     541     570     542
              570     540     570    1679     570    1679
              570     541     571     540     571    1685
              563    1679     570    1678     571    1678
              571   47910    9003    2231     570

          name KEY_1
             8969    4507     537     571     539     571
              540     572     539     572     539     572
              540     571     540     572     546     565
              539     572     540     571     540     571
              540     571     540    1709     540     572
              539     572     546     566     538    1709
              540    1709     540     572     539     572
              539     572     539     573     545     566
              538     572     539     573     538     572
              539    1710     540    1709     539    1712
              539    1709     539    1709     539    1710
              539   47930    8983    2261     539
...</pre></div><p>Now, we are ready to test our job. We have to verify if all buttons have been correctly recognized. To do it, we have to execute the <code class="literal">lircd</code> daemon from the command line as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# lircd --nodaemon --device /dev/lirc0 --driver default --uinput myremote.conf</strong></span>
<span class="strong"><strong>lircd-0.9.0-pre1[2235]: lircd(default) ready, using /var/run/lirc/lircd</strong></span>
</pre></div><p>The<a id="id492" class="indexterm"/> last argument <code class="literal">--uinput</code> is used to instruct the <code class="literal">lircd</code> daemon to convert the button presses into input events as they came from a normal keyboard, so, we can test them with the <code class="literal">evtest</code> command. It must be executed into another terminal due to the fact that the previous command must run with <code class="literal">evtest</code> at the same time! The command is as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# evtest</strong></span>
<span class="strong"><strong>No device specified, trying to scan all of /dev/input/event*</strong></span>
<span class="strong"><strong>Available devices:</strong></span>
<span class="strong"><strong>/dev/input/event0:      lircd</strong></span>
<span class="strong"><strong>Select the device event number [0-0]:</strong></span>
</pre></div><p>Now, we have to select the (only) available input device with the <code class="literal">0</code> number, and the program will continue showing the following output:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Input driver version is 1.0.1</strong></span>
<span class="strong"><strong>Input device ID: bus 0x0 vendor 0x0 product 0x0 version 0x0</strong></span>
<span class="strong"><strong>Input device name: "lircd"</strong></span>
<span class="strong"><strong>Supported events:</strong></span>
<span class="strong"><strong>  Event type 0 (EV_SYN)</strong></span>
<span class="strong"><strong>  Event type 1 (EV_KEY)</strong></span>
<span class="strong"><strong>    Event code 1 (KEY_ESC)</strong></span>
<span class="strong"><strong>    Event code 2 (KEY_1)</strong></span>
<span class="strong"><strong>    Event code 3 (KEY_2)</strong></span>
<span class="strong"><strong>    Event code 4 (KEY_3)</strong></span>
<span class="strong"><strong>    ...</strong></span>
<span class="strong"><strong>    Event code 237 (KEY_BLUETOOTH)</strong></span>
<span class="strong"><strong>    Event code 238 (KEY_WLAN)</strong></span>
<span class="strong"><strong>    Event code 239 (KEY_UWB)</strong></span>
<span class="strong"><strong>    Event code 240 (KEY_UNKNOWN)</strong></span>
<span class="strong"><strong>  Event type 20 (EV_REP)</strong></span>
<span class="strong"><strong>Properties:</strong></span>
<span class="strong"><strong>Testing ... (interrupt to exit)</strong></span>
</pre></div><p>Then, when I press a button on my remote controller, I get the following output:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Event: time 1445765562.506427, type 1 (EV_KEY), code 11 (KEY_0), value 1</strong></span>
<span class="strong"><strong>Event: time 1445765562.506427, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Event: time 1445765566.745716, type 1 (EV_KEY), code 2 (KEY_1), value 1</strong></span>
<span class="strong"><strong>Event: time 1445765566.745716, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Event: time 1445765568.216621, type 1 (EV_KEY), code 3 (KEY_2), value 1</strong></span>
<span class="strong"><strong>Event: time 1445765568.216621, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Event: time 1445765569.357041, type 1 (EV_KEY), code 4 (KEY_3), value 1</strong></span>
<span class="strong"><strong>Event: time 1445765569.357041, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip134"/>Tip</h3><p>The <code class="literal">evtest</code> program can be installed by using the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# aptitude install evtest</strong></span>
</pre></div></div></div><p>Note <a id="id493" class="indexterm"/>that the <span class="strong"><strong>0</strong></span> button on the remote controller corresponds to the <code class="literal">KEY_0</code> input event that has the <code class="literal">11</code> code, while the <span class="strong"><strong>1</strong></span>, <span class="strong"><strong>2</strong></span>, and <span class="strong"><strong>3</strong></span> buttons correspond to the <code class="literal">KEY_1</code>, <code class="literal">KEY_2</code>, and <code class="literal">KEY_3</code> input events that have the <code class="literal">2</code>, <code class="literal">3</code>, and <code class="literal">4</code> codes. So, we can map such events with the corresponding GPIO line by using a look-up table as follows (Python syntax):</p><div class="informalexample"><pre class="programlisting">GPIO = [-1, -1, 69, 44, 45, -1, -1, -1, -1, -1, -1, 68]</pre></div><p>The <code class="literal">-1</code> value means <span class="emphasis"><em>no GPIO</em></span>. So, when we press the <span class="strong"><strong>0</strong></span> button, we receive the <code class="literal">KEY_0</code> input event that has the <code class="literal">11</code> code, and at the 11th position of the array (starting the count from <span class="strong"><strong>0</strong></span>), we have the <code class="literal">68</code> value, so, the <code class="literal">GPIO68</code> is attached to the <span class="strong"><strong>0</strong></span> button on the remote controller. In a similar manner, the <span class="strong"><strong>1</strong></span>, <span class="strong"><strong>2</strong></span>, and <span class="strong"><strong>3</strong></span> buttons that correspond to the <code class="literal">KEY_1</code> (code 2), <code class="literal">KEY_2</code> (code 3), and <code class="literal">KEY_3</code> (code 4) input events, are connected to <code class="literal">GPIO 69</code> (array index 2), <code class="literal">GPIO 44</code> (array index 3), and <code class="literal">GPIO 45</code> (array index 4) respectively.</p></div><div class="section" title="The input events manager"><div class="titlepage"><div><div><h2 class="title"><a id="ch10lvl2sec86"/>The input events manager</h2></div></div></div><p>Now, we<a id="id494" class="indexterm"/> have to add the last element only; that is, the software that takes the input events and turns the corresponding relay on and off. To do it in a <span class="emphasis"><em>dirty and quick</em></span> way, we can use the Python language with the <code class="literal">evdev</code> library that can be easily installed on our BeagleBone Black with the following command:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# pip install evdev</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note108"/>Note</h3><p>The curious reader can<a id="id495" class="indexterm"/> get more information about this library at <a class="ulink" href="https://python-evdev.readthedocs.org/en/latest/">https://python-evdev.readthedocs.org/en/latest/</a>.</p></div></div><p>After the library has been installed, we can consider a possible implementation of our input events manager, as shown in the following code snippet:</p><div class="informalexample"><pre class="programlisting">#
# Local functions
#

def gpio_get(gpio):
   fd = open("/sys/class/gpio/gpio" + str(gpio) + "/value", "r")
   val = fd.read()
   fd.close()
return int(val)

def gpio_set(gpio, val):
   fd = open("/sys/class/gpio/gpio" + str(gpio) + "/value", "w")
   v = fd.write(str(val))
   fd.close()

def usage():
   print("usage: ", NAME, " [-h] &lt;inputdev&gt;", file=sys.stderr)
   sys.exit(2);

#
# Main
#

try:
   opts, args = getopt.getopt(sys.argv[1:], "h",
      ["help"])
except getopt.GetoptError, err:
   # Print help information and exit:
   print(str(err), file=sys.stderr)
   usage()

for o, a in opts:
   if o in ("-h", "--help"):
      usage()
   else:
      assert False, "unhandled option"

# Check command line
if len(args) &lt; 1:
   usage()

# Try to open the input device
try:
   dev = InputDevice(args[0])
except:
   print("invalid input device", args[0], file=sys.stderr)
   sys.exit(1);

logging.info (dev)
logging.info("hit CTRL+C to stop")

# Start the main loop
for event in dev.read_loop():
    if event.type == ecodes.EV_KEY and event.value == 1:
           # Get the key code and convert it to the corresponding GPIO
           code = event.code
           if code &lt; 0 or code &gt; len(GPIO):
                   gpio = -1
           else:
                   gpio = GPIO[code]
           logging.info("got code %d -&gt; GPIO%d" % (code, gpio))

           if gpio &gt; 0:
                   # Get current GPIO status and invert it
                   status = gpio_get(gpio)
                   status = 1 - status
                   gpio_set(gpio, status)
                   logging.info("turning GPIO%d %d -&gt; %d" %
                           (gpio, 1 - status, status))
           else:
                  logging.info("invalid button")</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="note109"/>Note</h3><p>The complete code is stored in the <code class="literal">chapter_10/read_events.py</code> file in the book's example code repository.</p></div></div><p>The<a id="id496" class="indexterm"/> code is quite self explanatory, but let me explain some points. First of all, note that the <code class="literal">GPIO</code> array is the one defined in the previous section, and then the <code class="literal">gpio_get()</code> and <code class="literal">gpio_set()</code> methods are used to get and set a GPIO status. The program, after a little check to the command line, starts opening the input device supplied by the user by using the <code class="literal">InputDevice()</code> method and then enters into the big loop, where it waits for a key press, and then it switches the status of the corresponding GPIO (if any).</p><p>The following is a sample usage:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# ./read_events.py /dev/input/event0</strong></span>
<span class="strong"><strong>INFO:root:device /dev/input/event0, name "lircd", phys ""</strong></span>
<span class="strong"><strong>INFO:root:hit CTRL+C to stop</strong></span>
<span class="strong"><strong>INFO:root:got code 2 -&gt; GPIO68</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO68 1 -&gt; 0</strong></span>
<span class="strong"><strong>INFO:root:got code 3 -&gt; GPIO69</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO69 1 -&gt; 0</strong></span>
<span class="strong"><strong>INFO:root:got code 3 -&gt; GPIO69</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO69 0 -&gt; 1</strong></span>
<span class="strong"><strong>INFO:root:got code 2 -&gt; GPIO68</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO68 0 -&gt; 1</strong></span>
</pre></div><p>Now, before continuing, let me suggest to you an interesting feature in using Linux's input layer.</p><p>Even if it may seem a bit complicated using the input layer instead of directly accessing the <code class="literal">lircd</code> daemon, this approach has the big advantage that we can test our relays manager with any input device! In fact, if you try to connect a normal keyboard to the BeagleBone Black's USB port, you'll get a new input device as follows:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# evtest</strong></span>
<span class="strong"><strong>No device specified, trying to scan all of /dev/input/event*</strong></span>
<span class="strong"><strong>Available devices:</strong></span>
<span class="strong"><strong>/dev/input/event0:   lircd</strong></span>
<span class="strong"><strong>/dev/input/event1:   HID 04d9:1203</strong></span>
<span class="strong"><strong>Select the device event number [0-1]:</strong></span>
</pre></div><p>Now, selecting the new input device <code class="literal">/dev/input/event1</code>, we can generate the same input events as before by simply pressing the <span class="strong"><strong>0</strong></span>, <span class="strong"><strong>1</strong></span>, <span class="strong"><strong>2</strong></span>, and <span class="strong"><strong>3</strong></span> keys:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>Event: time 1445766356.367407, type 4 (EV_MSC), code 4 (MSC_SCAN), value 70027</strong></span>
<span class="strong"><strong>Event: time 1445766356.367407, type 1 (EV_KEY), code 11 (KEY_0), value 1</strong></span>
<span class="strong"><strong>Event: time 1445766356.367407, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Event: time 1445766365.537391, type 4 (EV_MSC), code 4 (MSC_SCAN), value 7001e</strong></span>
<span class="strong"><strong>Event: time 1445766365.537391, type 1 (EV_KEY), code 2 (KEY_1), value 1</strong></span>
<span class="strong"><strong>Event: time 1445766365.537391, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Event: time 1445766367.437377, type 4 (EV_MSC), code 4 (MSC_SCAN), value 7001f</strong></span>
<span class="strong"><strong>Event: time 1445766367.437377, type 1 (EV_KEY), code 3 (KEY_2), value 1</strong></span>
<span class="strong"><strong>Event: time 1445766367.437377, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
<span class="strong"><strong>Event: time 1445766369.537383, type 4 (EV_MSC), code 4 (MSC_SCAN), value 70020</strong></span>
<span class="strong"><strong>Event: time 1445766369.537383, type 1 (EV_KEY), code 4 (KEY_3), value 1</strong></span>
<span class="strong"><strong>Event: time 1445766369.537383, -------------- SYN_REPORT ------------</strong></span>
<span class="strong"><strong>...</strong></span>
</pre></div><div class="note" title="Note" style=""><div class="inner"><h3 class="title"><a id="tip135"/>Tip</h3><p>Note that, even if not shown here due to lack of space, the keyboard generates more input events than the usual <code class="literal">EV_KEY</code> ones. But we can easily skip them just by selecting the right input event type.</p></div></div><p>In this <a id="id497" class="indexterm"/>situation, if we execute our program as in the following command line, we can manage the relays as we did with the remote controller:</p><div class="informalexample"><pre class="programlisting">
<span class="strong"><strong>root@arm:~# ./read_events.py /dev/input/event1</strong></span>
<span class="strong"><strong>INFO:root:device /dev/input/event1, name "HID 04d9:1203", phys "usb-musb-hdrc.1.auto-1/input0"</strong></span>
<span class="strong"><strong>INFO:root:hit CTRL+C to stop</strong></span>
<span class="strong"><strong>INFO:root:got code 11 -&gt; GPIO68</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO68 1 -&gt; 0</strong></span>
<span class="strong"><strong>INFO:root:got code 2 -&gt; GPIO69</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO69 1 -&gt; 0</strong></span>
<span class="strong"><strong>INFO:root:got code 3 -&gt; GPIO44</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO44 1 -&gt; 0</strong></span>
<span class="strong"><strong>INFO:root:got code 4 -&gt; GPIO45</strong></span>
<span class="strong"><strong>INFO:root:turning GPIO45 1 -&gt; 0</strong></span>
</pre></div></div></div></body></html>
