<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 909 1286" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_6{
fill: #ED028C;
}
.g1_6{
fill: none;
stroke: #ED028C;
stroke-width: 1.5155555;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_6{
fill: none;
stroke: #939598;
stroke-width: 6.106528;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g3_6{
fill: #F0F1F0;
}
.g4_6{
fill: #FFFFFF;
}
.g5_6{
fill: #231F20;
}
.g6_6{
fill: #ECF3F2;
}
.g7_6{
fill: #D2DAD9;
}
.g8_6{
fill: #536261;
}
.g9_6{
fill: #F9F4E0;
}
.g10_6{
fill: #D5212D;
}
.g11_6{
fill: #EE2028;
}
.g12_6{
fill: #AE2931;
}
.g13_6{
fill: #BFC7C7;
}
.g14_6{
fill: #F15C59;
}
.g15_6{
fill: #EA2E33;
}
.g16_6{
fill: none;
stroke: #231F20;
stroke-width: 1.5155555;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g17_6{
fill: #FBDFEB;
}
]]></style>
</defs>
<path d="M42.2,1189.1H92.4v-25H42.2v25Z" class="g0_6" />
<path d="M42.2,1189.8h770" class="g1_6" />
<path d="M252.7,116.6V1126.7" class="g2_6" />
<path fill-rule="evenodd" d="M154.9,244L131.5,148.7L121.3,138.6l-58,-21.1L45.2,208.9L154.9,244Z" class="g3_6" />
<path fill-rule="evenodd" d="M153.8,239.9L130.9,151.8L120.6,141.1L66.9,117.9L49,206.8l104.8,33.1Z" class="g4_6" />
<path fill-rule="evenodd" d="M131,149l.3,.4l21.8,87.8l-.7,-.3L130.3,149.3l.3,.4l.4,-.7Z" class="g5_6" />
<path fill-rule="evenodd" d="M120.5,139.2l.2,.1l10.2,9.7l-.3,.8L120.2,140l.3,.2v-1Z" class="g5_6" />
<path fill-rule="evenodd" d="M65.6,118.3l54.9,20.9v1l-55,-20.8l.1,-1.1Z" class="g5_6" />
<path fill-rule="evenodd" d="M48.7,204.1L65.6,118.7l1.1,.5L50,204.5l-1.3,-.4Z" class="g5_6" />
<path fill-rule="evenodd" d="M153.2,237.3L49.7,204.4v-1.1l103.4,33.2l.1,.8Z" class="g5_6" />
<path fill-rule="evenodd" d="M130.7,149.5l-.5,-.5l-.7,-.7l-.8,-.6l-.5,-.5l-.7,-.8l-.6,-.5l-.7,-.7l-.7,-.6l-.6,-.5l-.7,-.8l-.6,-.5l-.7,-.6l-.6,-.6l-.7,-.6l-.6,-.5l-.5,-.7l.1,.8l.1,.8l.1,.6l.2,.9l.1,.8l.1,.8l.1,.6l.2,.9l5.2,2l.6,.2l3.4,1.3Z" class="g6_6" />
<path fill-rule="evenodd" d="M119.9,139.7l.8,-.2l.7,.6l.5,.6l.6,.6l.7,.6l.6,.5l.7,.6l.6,.7l.7,.6l.6,.6l.7,.6l.7,.6l.6,.7l.7,.6l.7,.7l.6,.5l.7,.6l-.5,.7l-.8,-.6l-.7,-.7l-.5,-.5l-.8,-.6l-.5,-.7l-.7,-.6l-.6,-.6l-.7,-.6l-.7,-.6l-.6,-.7l-.7,-.6l-.6,-.5l-.7,-.7l-.6,-.5l-.7,-.6l-.5,-.7l.7,-.1l-1,-.3Z" class="g5_6" />
<path fill-rule="evenodd" d="M121.5,146.6l-.4,-.6l-.3,-.9v-.8l-.1,-.6l-.3,-.9l-.1,-.8l-.1,-.6l-.1,-.8l-.2,-.9l1,.3l.1,.6l.1,.8l.3,.9l.1,.6l.1,.8l.1,.8l.2,.9l.1,.6l-.4,-.4l-.1,1Z" class="g5_6" />
<path fill-rule="evenodd" d="M131.1,149.2l-.4,.9l-9.2,-3.5l.1,-1l7.5,2.8l.5,.2l1.2,.5l-.2,.8l.5,-.7Z" class="g5_6" />
<path fill-rule="evenodd" d="M127.7,157.5L71.2,135.9l-.5,2.2l57.6,22.1l-.6,-2.7Z" class="g7_6" />
<path fill-rule="evenodd" d="M129.3,165.1L70,142.5l-.3,2.6l60.2,23.1l-.6,-3.1Z" class="g7_6" />
<path fill-rule="evenodd" d="M131.1,173.6L68.9,149.8l-.4,2.9l63.2,24.2l-.6,-3.3Z" class="g7_6" />
<path fill-rule="evenodd" d="M133,182.9l-65.3,-25l-.6,3.2l66.5,25.4l-.6,-3.6Z" class="g7_6" />
<path fill-rule="evenodd" d="M134.9,193.1L66.2,166.8l-.6,3.6l70.2,26.9l-.9,-4.2Z" class="g7_6" />
<path fill-rule="evenodd" d="M137.3,204.6L64.7,176.8l-.6,4l74,28.4l-.8,-4.6Z" class="g7_6" />
<path fill-rule="evenodd" d="M99.3,148.6l-1.2,1.8L96,147.2V147l-.1,-.2l-.1,-.2l.2,-.1l.2,-.1l.1,-.2l.2,.1l.3,-.2l3.6,.7l-1.1,1.8Z" class="g8_6" />
<path fill-rule="evenodd" d="M98.1,150.4l5.8,9.2l4.7,-4.7l2.5,-6.8l-10.7,-1.3l-2.3,3.6Z" class="g9_6" />
<path fill-rule="evenodd" d="M109.3,151.4l-.4,.1l-.5,.1l-.3,.2l-.5,.2l-.3,.3l-.3,.3l-.3,.2l-.1,.4l-.3,.3l-.2,.4v.5l-.1,.4v1.6L144.6,181l3.2,-5L109.3,151.4Z" class="g10_6" />
<path fill-rule="evenodd" d="M109.2,151.4v-.3l-.2,-.3v-.7l.1,-.3l.2,-.3v-.3l.1,-.3l.3,-.2l.2,-.2l.1,-.1l.3,-.1l.2,-.1l.3,-.2l.3,.1h.2l38.6,24.5l-2.1,3.4L109.2,151.4Z" class="g11_6" />
<path fill-rule="evenodd" d="M104.1,159.6l-.2,-.3V159l-.2,-.3v-.6l.2,-.2l.1,-.3l.2,-.3l.1,-.1l.2,-.3l.3,-.1l.1,-.2l.3,-.1l.3,-.1l.4,-.1h.3l38.6,24.6l-2.1,3.3L104.1,159.6Z" class="g12_6" />
<path fill-rule="evenodd" d="M149.9,172.6l-7.2,11.6l6.9,4.5l7.3,-11.6l-7,-4.5Z" class="g6_6" />
<path fill-rule="evenodd" d="M143.9,182.1l-1.4,2.1l6.5,4l1.4,-2.1l-6.5,-4Z" class="g13_6" />
<path fill-rule="evenodd" d="M162.6,183.7l-4.7,7.2l-.3,.4l-.3,.3l-.4,.3l-.4,.2l-.4,.1h-.5l-.5,-.1l-.3,-.1l-5.2,-3.3L157,177.1l5.1,3.3h.1l.2,.2l.1,.1l.2,.2v.3l.2,.2v.4l.1,.2v.3l-.1,.2v.5l-.2,.3l-.1,.1v.3Z" class="g14_6" />
<path fill-rule="evenodd" d="M151.1,186.5l4.9,3.2l.5,.2l.3,.2h.3l.8,.1l.3,-.1l.3,-.3l.3,-.1l-.9,1.4l-.3,.4l-.4,.2l-.5,.3l-.5,.1l-.4,.1h-.5l-.5,-.2l-.3,-.4l-4.9,-2.9l1.5,-2.2Z" class="g15_6" />
<path fill-rule="evenodd" d="M157.1,191H157l2.7,-4l.8,.5l-2.6,4.2l-.1,.1l-.7,-.8Z" class="g5_6" />
<path fill-rule="evenodd" d="M155.1,191.7l-.1,-.2l.1,.2h.4l.1,-.1h.3l.3,-.1l.3,-.1l.3,-.1l.3,-.3l.7,.8l-.3,.3l-.5,.2l-.4,.1l-.4,.1l-.4,.1h-.3l-.8,-.1l-.2,-.1l.6,-.7Z" class="g5_6" />
<path fill-rule="evenodd" d="M104.4,159.5l-.2,-.1l50.9,32.3l-.6,.7L103.8,160.2l-.2,-.3l.8,-.4Z" class="g5_6" />
<path fill-rule="evenodd" d="M96.5,147l7.9,12.5l-.8,.4l-8,-12.5l.9,-.4Z" class="g5_6" />
<path fill-rule="evenodd" d="M96.5,146.8v.2l-.9,.4l-.1,-.2l-.1,-.2v-.6l.1,-.1v-.1l1,.6Z" class="g5_6" />
<path fill-rule="evenodd" d="M96.6,146.7h-.1v.1l-1,-.6l.2,-.2l.3,-.1l.3,-.1h.3v.9Z" class="g5_6" />
<path fill-rule="evenodd" d="M111,148.5l.3,.2l-14.7,-2v-.9l14.7,1.8l.3,.2l-.6,.7Z" class="g5_6" />
<path fill-rule="evenodd" d="M161.7,180.7l.2,.1L111,148.5l.6,-.7L162.4,180l.1,.1l-.8,.6Z" class="g5_6" />
<path fill-rule="evenodd" d="M162.3,182.9h-.2l.2,-.4l-.1,-.3v-.6l-.1,-.3v-.2l-.2,-.1l-.2,-.3l.8,-.6l.2,.4l.1,.3l.2,.3l.2,.3v.4l.1,.5l-.1,.4v.6l-.1,.1l-.8,-.5Z" class="g5_6" />
<path fill-rule="evenodd" d="M159.7,187l2.6,-4.1l.8,.5l-2.6,4.1l-.8,-.5Z" class="g5_6" />
<path fill-rule="evenodd" d="M139,217.3L62.7,188.1l-.6,4.3l77.8,29.8l-.9,-4.9Z" class="g7_6" />
<path d="M149.6,225.7h99.1m-204.6,0H95.7" class="g16_6" />
<image preserveAspectRatio="none" x="261" y="116" width="550" height="29" xlink:href="/funlearn/downloadEpubImage?source=upload/books/347/chapters/3054/6523/extract/Chapter-1(1)/6/img/1.png" />
<path d="M43,840H242.8V438.6H43V840Z" class="g17_6" />
</svg>