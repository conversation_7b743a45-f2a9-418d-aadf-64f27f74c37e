<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1210" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_5{
fill: #B8282E;
}
.g1_5{
fill: none;
stroke: #B8282E;
stroke-width: 6.0499997;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g2_5{
fill: #F0F1F0;
}
.g3_5{
fill: #FFFFFF;
}
.g4_5{
fill: #231F20;
}
.g5_5{
fill: #ECF3F2;
}
.g6_5{
fill: #D2DAD9;
}
.g7_5{
fill: #536261;
}
.g8_5{
fill: #F9F4E0;
}
.g9_5{
fill: #D5222D;
}
.g10_5{
fill: #EE2028;
}
.g11_5{
fill: #AE2930;
}
.g12_5{
fill: #BFC7C7;
}
.g13_5{
fill: #F15C5A;
}
.g14_5{
fill: #EA2E33;
}
.g15_5{
fill: none;
stroke: #231F20;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g16_5{
fill: #2E3092;
}
.g17_5{
fill: none;
stroke: #2E3092;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g18_5{
fill: none;
stroke: #2E3092;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
}
.g19_5{
fill: #FFF9BC;
}
.g20_5{
fill: none;
stroke: #231F20;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g21_5{
fill: #B4E3F9;
}
.g22_5{
fill: none;
stroke: #FFFFFF;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: round;
}
.g23_5{
fill: none;
stroke: #231F20;
stroke-width: 1.4666667;
stroke-linecap: butt;
stroke-linejoin: round;
}
]]></style>
</defs>
<path fill-rule="evenodd" d="M107.8,111.5H269v-29H107.8v29Z" class="g0_5" />
<path d="M730.1,82.5V1121.1" class="g1_5" />
<path fill-rule="evenodd" d="M801.2,181.7L818,107.5l7.3,-7.9L867,83.2l13,71.1l-78.8,27.4Z" class="g2_5" />
<path fill-rule="evenodd" d="M802,178.5l16.4,-68.7l7.4,-8.3l38.6,-18l12.9,69.2L802,178.5Z" class="g3_5" />
<path fill-rule="evenodd" d="M818.4,107.7l-.2,.3l-15.7,68.4l.5,-.3l15.9,-68.2l-.2,.3l-.3,-.5Z" class="g4_5" />
<path fill-rule="evenodd" d="M825.9,100.1h-.1l-7.4,7.6l.3,.6l7.4,-7.6l-.2,.2v-.8Z" class="g4_5" />
<path fill-rule="evenodd" d="M865.3,83.7l-39.4,16.4v.8L865.4,84.6l-.1,-.9Z" class="g4_5" />
<path fill-rule="evenodd" d="M877.4,150.6L865.3,84.1l-.7,.4l11.9,66.4l.9,-.3Z" class="g4_5" />
<path fill-rule="evenodd" d="M802.4,176.4l74.3,-25.6l.1,-.8l-74.3,25.8l-.1,.6Z" class="g4_5" />
<path fill-rule="evenodd" d="M818.6,108.1l.4,-.4l.5,-.6l.5,-.4l.4,-.4l.5,-.6l.4,-.4l.5,-.6l.5,-.4l.5,-.4l.5,-.6l.4,-.4l.5,-.5l.4,-.5l.5,-.5l.4,-.4l.4,-.5v.6l-.1,.7l-.1,.5l-.2,.6v.7l-.1,.6l-.1,.5l-.1,.6l-.5,.2l-.4,.2l-.3,.1l-.4,.2l-.5,.2l-.4,.2l-.4,.1l-.4,.2l-.5,.2l-.4,.2l-.4,.2l-.5,.1l-.4,.2l-.3,.1l-.4,.2l-.4,.2Z" class="g5_5" />
<path fill-rule="evenodd" d="M826.4,100.5l-.6,-.2l-.5,.5l-.4,.4l-.4,.5l-.5,.5l-.5,.4l-.5,.4l-.4,.5l-.5,.5l-.4,.5l-.5,.5l-.5,.4l-.4,.6l-.6,.4l-.5,.6l-.4,.4l-.5,.4l.4,.6l.5,-.5l.5,-.5l.4,-.4l.5,-.5l.4,-.5l.6,-.5l.4,-.5l.5,-.4l.5,-.5l.4,-.5l.5,-.5l.4,-.4l.5,-.5l.4,-.5l.6,-.4l.4,-.5l-.6,-.2l.8,-.1Z" class="g4_5" />
<path fill-rule="evenodd" d="M825.2,105.8l.3,-.5l.2,-.6V104l.1,-.5l.2,-.6v-.7l.1,-.5l.1,-.6l.2,-.6l-.8,.1l-.1,.6v.6l-.2,.6l-.1,.5l-.1,.7v.6l-.2,.7l-.1,.5l.3,-.4l.1,.8Z" class="g4_5" />
<path fill-rule="evenodd" d="M818.3,107.8l.3,.8l.4,-.2l.4,-.2l.4,-.1l.4,-.2l.4,-.2l.4,-.2l.5,-.1l.4,-.2l.4,-.2l.4,-.2l.5,-.1l.4,-.2l.4,-.2l.3,-.1l.5,-.2l.4,-.2l-.1,-.8l-.4,.2l-.4,.2l-.3,.1l-.5,.2l-.4,.1l-.4,.2l-.4,.2l-.5,.2l-.4,.2l-.4,.1l-.4,.2l-.5,.2l-.4,.2l-.3,.1l-.4,.2l-.5,.1l.2,.7l-.4,-.6Z" class="g4_5" />
<path fill-rule="evenodd" d="M820.7,114.3L841,105.9l20.3,-8.4l.4,1.7L841,107.8l-20.7,8.6l.4,-2.1Z" class="g6_5" />
<path fill-rule="evenodd" d="M819.6,120.3l21.2,-8.8l21.3,-8.9l.3,2.1l-21.6,8.9l-21.7,9l.5,-2.3Z" class="g6_5" />
<path fill-rule="evenodd" d="M818.3,126.9l22.3,-9.3L863,108.3l.2,2.3L840.5,120l-22.7,9.4l.5,-2.5Z" class="g6_5" />
<path fill-rule="evenodd" d="M817,134.1l23.4,-9.7l23.5,-9.8l.3,2.5L840.4,127l-23.9,9.9l.5,-2.8Z" class="g6_5" />
<path fill-rule="evenodd" d="M815.5,142.1l24.7,-10.3l24.7,-10.2l.4,2.8l-25.1,10.4l-25.3,10.5l.6,-3.2Z" class="g6_5" />
<path fill-rule="evenodd" d="M813.9,151L840,140.1l26,-10.8l.4,3.2l-26.6,11l-26.6,11.1l.7,-3.6Z" class="g6_5" />
<path fill-rule="evenodd" d="M841.1,107.4l.9,1.4l1.5,-2.5v-.2l.1,-.1v-.1l-.1,-.2l-.1,-.1l-.1,-.1l-.1,.1l-.3,-.1l-2.5,.4l.7,1.5Z" class="g7_5" />
<path fill-rule="evenodd" d="M842,108.8l-4.2,7.2l-3.4,-3.7L832.6,107l7.8,-1.1l1.6,2.9Z" class="g8_5" />
<path fill-rule="evenodd" d="M833.9,109.6l.4,.1h.3l.2,.1l.4,.2l.2,.2l.2,.3l.2,.2l.1,.3l.2,.2l.1,.3v.4l.1,.3v1.3l-27.7,19.1l-2.3,-3.9l27.6,-19.1Z" class="g9_5" />
<path fill-rule="evenodd" d="M834,109.6v-.3l.2,-.2v-.6l-.1,-.2l-.1,-.2v-.2l-.1,-.3l-.3,-.2l-.1,-.1l-.1,-.1l-.2,-.1l-.1,-.1l-.2,-.1l-.3,.1h-.1l-27.7,19.1l1.5,2.6L834,109.6Z" class="g10_5" />
<path fill-rule="evenodd" d="M837.7,116l.1,-.3v-.2l.2,-.2v-.5l-.1,-.2l-.2,-.2l-.1,-.3l-.1,-.1l-.1,-.2l-.2,-.1l-.1,-.1l-.2,-.1l-.3,-.1h-.4l-27.7,19.1l1.5,2.6L837.7,116Z" class="g11_5" />
<path fill-rule="evenodd" d="M804.8,126.1l5.2,9l-5,3.5l-5.2,-9l5,-3.5Z" class="g5_5" />
<path fill-rule="evenodd" d="M809.1,133.5l1,1.6l-4.7,3.1l-.9,-1.6l4.6,-3.1Z" class="g12_5" />
<path fill-rule="evenodd" d="M795.7,134.7l3.3,5.6l.3,.4l.2,.2l.3,.2l.2,.2l.4,.1l.3,-.1h.3l.3,-.2l3.7,-2.5l-5.3,-9l-3.6,2.5l-.1,.1l-.2,.1l-.1,.1l-.1,.1v.3l-.1,.1v.3l-.1,.2v.2l.1,.2v.4l.1,.2l.1,.1v.2Z" class="g13_5" />
<path fill-rule="evenodd" d="M803.9,136.9l-3.5,2.5l-.3,.1l-.3,.2h-.5l-.3,.1l-.2,-.1l-.2,-.2l-.2,-.1l.6,1.1l.3,.3l.3,.2l.3,.2h.3l.4,.1h.3l.4,-.2l.2,-.2l3.5,-2.3l-1.1,-1.7Z" class="g14_5" />
<path fill-rule="evenodd" d="M799.6,140.4h.1l-1.9,-3.1l-.6,.4l1.8,3.2l.1,.1l.5,-.6Z" class="g4_5" />
<path fill-rule="evenodd" d="M801,140.9l.1,-.1l-.1,.1l-.2,.1l-.1,-.1h-.2l-.2,-.1l-.3,-.1l-.2,-.1l-.2,-.2l-.5,.6l.3,.2l.3,.2l.3,.1l.2,.1l.4,.1h.2l.2,-.1h.4l.1,-.1l-.5,-.6Z" class="g4_5" />
<path fill-rule="evenodd" d="M837.5,115.9l.1,-.1L801,140.9l.5,.6l36.4,-25.1l.1,-.2l-.5,-.3Z" class="g4_5" />
<path fill-rule="evenodd" d="M843.2,106.1v0l-5.7,9.8l.5,.3l5.8,-9.8l-.6,-.3Z" class="g4_5" />
<path fill-rule="evenodd" d="M843.2,106v.1l.6,.3v-.1l.1,-.1v-.5l-.1,-.1v-.1l-.6,.5Z" class="g4_5" />
<path fill-rule="evenodd" d="M843,105.9v0h.2v.1l.6,-.5l-.1,-.1l-.2,-.1l-.2,-.1h-.2l-.1,.7Z" class="g4_5" />
<path fill-rule="evenodd" d="M832.7,107.3l-.2,.1L843,105.9l.1,-.7l-10.6,1.4l-.2,.2l.4,.5Z" class="g4_5" />
<path fill-rule="evenodd" d="M796.3,132.3l-.1,.2l36.5,-25.2l-.4,-.5l-36.4,25l-.2,.1l.6,.4Z" class="g4_5" />
<path fill-rule="evenodd" d="M795.9,134.1h.1l-.1,-.3v-.7l.2,-.3v-.1l.1,-.1l.1,-.3l-.6,-.4l-.1,.3l-.1,.2l-.1,.3l-.1,.2v.4l-.2,.3l.2,.3l-.1,.5l.2,.1l.5,-.4Z" class="g4_5" />
<path fill-rule="evenodd" d="M797.8,137.3v0l-1.9,-3.2l-.5,.4l1.8,3.2l.6,-.4Z" class="g4_5" />
<path fill-rule="evenodd" d="M812.6,160.9L840,149.5l27.4,-11.3l.4,3.3l-28,11.6l-27.9,11.6l.7,-3.8Z" class="g6_5" />
<path d="M735,168.5h68.2m35.6,0h41.9" class="g15_5" />
<path fill-rule="evenodd" d="M830.8,1154.4h48.8v-20.1H830.8v20.1Z" class="g16_5" />
<path d="M830.8,1154.4h48.8v-20.1H830.8v20.1Z" class="g17_5" />
<path d="M110.4,1155.4H181m7.3,0h15.2m5.1,0h15.2m5.2,0h15.2m5.1,0h15.2m5.2,0h15.2m5.1,0h15.2m5.2,0h15.2m5.1,0h15.2m5.2,0h15.2m5.1,0h15.2m5.2,0H407m5.1,0h15.2m5,0h15.2m5,0h15.2m4.9,0h15.2m5,0H508m5,0h15.2m4.9,0h15.2m5,0h15.2m5,0h15.2m4.9,0h15.2m5,0H629m5,0h15.2m4.9,0h15.2m5,0h15.2m4.9,0h15.3m4.9,0h15.2m5,0H750m4.9,0h15.3m4.9,0h15.2m5,0h15.2m4.9,0h15.3" class="g18_5" />
<path fill-rule="evenodd" d="M110.7,906.9H714.3V149.2H110.7V906.9Z" class="g19_5" />
<path d="M110.7,906.9H714.3V149.2H110.7V906.9Z" class="g20_5" />
<path fill-rule="evenodd" d="M160.2,167.6c6.5,-5.9,14.5,-8.9,23.7,-8.9H385.8c9.3,0,17.3,3,23.8,8.9c6.6,5.9,9.9,13,9.9,21.3c0,8.4,-3.3,15.5,-9.9,21.4c-6.5,5.9,-14.5,8.9,-23.8,8.9H183.9c-9.2,0,-17.2,-3,-23.7,-8.9c-6.6,-5.9,-9.9,-13,-9.9,-21.4c0,-8.3,3.3,-15.4,9.9,-21.3Z" class="g21_5" />
<path d="M160.2,167.6c6.5,-5.9,14.5,-8.9,23.7,-8.9H385.8c9.3,0,17.3,3,23.8,8.9c6.6,5.9,9.9,13,9.9,21.3c0,8.4,-3.3,15.5,-9.9,21.4c-6.5,5.9,-14.5,8.9,-23.8,8.9H183.9c-9.2,0,-17.2,-3,-23.7,-8.9c-6.6,-5.9,-9.9,-13,-9.9,-21.4c0,-8.3,3.3,-15.4,9.9,-21.3Z" class="g20_5" />
<path fill-rule="evenodd" d="M129,190.9c0,-9.1,3.2,-16.8,9.6,-23.2c6.3,-6.3,14.1,-9.5,23.1,-9.5c9,0,16.7,3.2,23.1,9.5c6.4,6.4,9.6,14.1,9.6,23.2c0,9,-3.2,16.7,-9.6,23.1c-6.4,6.4,-14.1,9.5,-23.1,9.5c-9,0,-16.8,-3.1,-23.1,-9.5C132.2,207.6,129,199.9,129,190.9Z" class="g3_5" />
<path d="M129,190.9c0,-9.1,3.2,-16.8,9.6,-23.2c6.3,-6.3,14.1,-9.5,23.1,-9.5c9,0,16.7,3.2,23.1,9.5c6.4,6.4,9.6,14.1,9.6,23.2c0,9,-3.2,16.7,-9.6,23.1c-6.4,6.4,-14.1,9.5,-23.1,9.5c-9,0,-16.8,-3.1,-23.1,-9.5C132.2,207.6,129,199.9,129,190.9Z" class="g22_5" />
<path fill-rule="evenodd" d="M117.8,191.8c0,-9.1,3.2,-16.8,9.6,-23.1c6.4,-6.4,14.1,-9.6,23.1,-9.6c9.1,0,16.8,3.2,23.1,9.6c6.4,6.3,9.6,14,9.6,23.1c0,9,-3.2,16.7,-9.6,23.1c-6.3,6.4,-14,9.6,-23.1,9.6c-9,0,-16.7,-3.2,-23.1,-9.6c-6.4,-6.4,-9.6,-14.1,-9.6,-23.1Z" class="g21_5" />
<path d="M117.8,191.8c0,-9.1,3.2,-16.8,9.6,-23.1c6.4,-6.4,14.1,-9.6,23.1,-9.6c9.1,0,16.8,3.2,23.1,9.6c6.4,6.3,9.6,14,9.6,23.1c0,9,-3.2,16.7,-9.6,23.1c-6.3,6.4,-14,9.6,-23.1,9.6c-9,0,-16.7,-3.2,-23.1,-9.6c-6.4,-6.4,-9.6,-14.1,-9.6,-23.1Z" class="g23_5" />
</svg>