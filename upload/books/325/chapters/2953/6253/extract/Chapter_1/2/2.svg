<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 909 1286" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_2{
fill: #231F20;
}
.g1_2{
fill: none;
stroke: #231F20;
stroke-width: 0.57294786;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g2_2{
fill: none;
stroke: #231F20;
stroke-width: 0.47724783;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g3_2{
fill: none;
stroke: #939598;
stroke-width: 1.0692459;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g4_2{
fill: none;
stroke: #939598;
stroke-width: 0.6877145;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g5_2{
fill: none;
stroke: #231F20;
stroke-width: 0.57294786;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g6_2{
fill: none;
stroke: #231F20;
stroke-width: 1.1459104;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g7_2{
fill: none;
stroke: #231F20;
stroke-width: 0.47724783;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g8_2{
fill: none;
stroke: #231F20;
stroke-width: 0.95492285;
stroke-linecap: round;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g9_2{
fill: none;
stroke: #231F20;
stroke-width: 1.5278689;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g10_2{
fill: none;
stroke: #231F20;
stroke-width: 0.76220834;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
]]></style>
</defs>
<path fill-rule="evenodd" d="M206.2,196.1h25.4H206.2Z" class="g0_2" />
<path d="M206.2,196.1h25.4" class="g1_2" />
<path fill-rule="evenodd" d="M294.5,196.1h25.3H294.5Z" class="g0_2" />
<path d="M294.5,196.1h25.3" class="g1_2" />
<path fill-rule="evenodd" d="M149.2,358.5H280.8H149.2Z" class="g0_2" />
<path d="M149.2,358.5H280.8" class="g2_2" />
<path fill-rule="evenodd" d="M240,398.6h21.6H240Z" class="g0_2" />
<path d="M240,398.6h21.6" class="g2_2" />
<path d="M118.1,226H469.2" class="g3_2" />
<path d="M118.1,244.9H469.2" class="g4_2" />
<path d="M118.1,420.8H469.2" class="g3_2" />
<path d="M390.7,225.4v196" class="g4_2" />
<path fill-rule="evenodd" d="M323.5,546h17.3H323.5Z" class="g0_2" />
<path d="M323.5,546h17.3" class="g1_2" />
<path fill-rule="evenodd" d="M402,546h17.3H402Z" class="g0_2" />
<path d="M402,546h17.3" class="g1_2" />
<path fill-rule="evenodd" d="M186,582.6l1.8,-1l-1.8,1Z" class="g0_2" />
<path d="M186,582.6l1.8,-1" class="g5_2" />
<path fill-rule="evenodd" d="M187.8,581.8l2.8,5.1l-2.8,-5.1Z" class="g0_2" />
<path d="M187.8,581.8l2.8,5.1" class="g6_2" />
<path fill-rule="evenodd" d="M190.8,586.9l3.6,-14.8l-3.6,14.8Z" class="g0_2" />
<path d="M190.8,586.9l3.6,-14.8" class="g5_2" />
<path fill-rule="evenodd" d="M194.4,572.1h17.3H194.4Z" class="g0_2" />
<path d="M194.4,572.1h17.3" class="g5_2" />
<path fill-rule="evenodd" d="M119.9,639H243H119.9Z" class="g0_2" />
<path d="M119.9,639H243" class="g1_2" />
<path fill-rule="evenodd" d="M466.9,708.5h49.4H466.9Z" class="g0_2" />
<path d="M466.9,708.5h49.4" class="g1_2" />
<path fill-rule="evenodd" d="M186,743.5l1.8,-1l-1.8,1Z" class="g0_2" />
<path d="M186,743.5l1.8,-1" class="g5_2" />
<path fill-rule="evenodd" d="M187.8,742.8l2.8,6.3l-2.8,-6.3Z" class="g0_2" />
<path d="M187.8,742.8l2.8,6.3" class="g6_2" />
<path fill-rule="evenodd" d="M190.8,749.1l3.6,-18.2l-3.6,18.2Z" class="g0_2" />
<path d="M190.8,749.1l3.6,-18.2" class="g5_2" />
<path fill-rule="evenodd" d="M194.4,730.9h19.5H194.4Z" class="g0_2" />
<path d="M194.4,730.9h19.5" class="g5_2" />
<path fill-rule="evenodd" d="M263.3,787.8h15.6H263.3Z" class="g0_2" />
<path d="M263.3,787.8h15.6" class="g2_2" />
<path fill-rule="evenodd" d="M355.5,787.8h15.6H355.5Z" class="g0_2" />
<path d="M355.5,787.8h15.6" class="g2_2" />
<path fill-rule="evenodd" d="M274.8,822.4h15.6H274.8Z" class="g0_2" />
<path d="M274.8,822.4h15.6" class="g2_2" />
<path fill-rule="evenodd" d="M378.4,822.4h15.7H378.4Z" class="g0_2" />
<path d="M378.4,822.4h15.7" class="g2_2" />
<path fill-rule="evenodd" d="M147.5,935.7h41.4H147.5Z" class="g0_2" />
<path d="M147.5,935.7h41.4" class="g1_2" />
<path fill-rule="evenodd" d="M158.6,994.9l1.7,-1l-1.7,1Z" class="g0_2" />
<path d="M158.6,994.9l1.7,-1" class="g7_2" />
<path fill-rule="evenodd" d="M160.3,994.2l2.5,6.8l-2.5,-6.8Z" class="g0_2" />
<path d="M160.3,994.2l2.5,6.8" class="g8_2" />
<path fill-rule="evenodd" d="M163,1001l3.2,-19.1L163,1001Z" class="g0_2" />
<path d="M163,1001l3.2,-19.1" class="g7_2" />
<path fill-rule="evenodd" d="M166.2,981.9h36.4H166.2Z" class="g0_2" />
<path d="M166.2,981.9h36.4" class="g7_2" />
<path fill-rule="evenodd" d="M170.8,1018.7l1.7,-1l-1.7,1Z" class="g0_2" />
<path d="M170.8,1018.7l1.7,-1" class="g7_2" />
<path fill-rule="evenodd" d="M172.5,1018l2.5,6.8l-2.5,-6.8Z" class="g0_2" />
<path d="M172.5,1018l2.5,6.8" class="g8_2" />
<path fill-rule="evenodd" d="M175.2,1024.8l3.2,-19.1l-3.2,19.1Z" class="g0_2" />
<path d="M175.2,1024.8l3.2,-19.1" class="g7_2" />
<path fill-rule="evenodd" d="M178.4,1005.7h35.2H178.4Z" class="g0_2" />
<path d="M178.4,1005.7h35.2" class="g7_2" />
<path fill-rule="evenodd" d="M159.8,1042.5l1.7,-1l-1.7,1Z" class="g0_2" />
<path d="M159.8,1042.5l1.7,-1" class="g7_2" />
<path fill-rule="evenodd" d="M161.5,1041.8l2.4,6.8l-2.4,-6.8Z" class="g0_2" />
<path d="M161.5,1041.8l2.4,6.8" class="g8_2" />
<path fill-rule="evenodd" d="M164.2,1048.6l3.1,-19.1l-3.1,19.1Z" class="g0_2" />
<path d="M164.2,1048.6l3.1,-19.1" class="g7_2" />
<path fill-rule="evenodd" d="M167.3,1029.5h36.4H167.3Z" class="g0_2" />
<path d="M167.3,1029.5h36.4" class="g7_2" />
<path fill-rule="evenodd" d="M172,1066.3l1.6,-.9l-1.6,.9Z" class="g0_2" />
<path d="M172,1066.3l1.6,-.9" class="g7_2" />
<path fill-rule="evenodd" d="M173.6,1065.6l2.5,6.8l-2.5,-6.8Z" class="g0_2" />
<path d="M173.6,1065.6l2.5,6.8" class="g8_2" />
<path fill-rule="evenodd" d="M176.4,1072.4l3.1,-19.1l-3.1,19.1Z" class="g0_2" />
<path d="M176.4,1072.4l3.1,-19.1" class="g7_2" />
<path fill-rule="evenodd" d="M179.5,1053.3h35.3H179.5Z" class="g0_2" />
<path d="M179.5,1053.3h35.3" class="g7_2" />
<path d="M83.2,147.7H825.7" class="g9_2" />
<path d="M703.7,1142.9h110c5.8,0,10.5,5,10.5,11c0,6,-4.7,10.9,-10.5,10.9h-110c-5.8,0,-10.5,-4.9,-10.5,-10.9c0,-6,4.7,-11,10.5,-11Z" class="g10_2" />
</svg>