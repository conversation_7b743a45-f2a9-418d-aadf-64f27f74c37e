@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
}

.subjectHead {
	color:#FF0080;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}

.chapterHeading {
	font-size:1.5em;
	margin:5% 0;
	color:#ffffff;
	font-weight:bold;
    background:#2C82DD;
    padding:15px;
	text-shadow: 1px 1px 1px #000;
    -webkit-box-shadow: 6px 6px 5px 0px rgba(0,0,0,0.60);
	-moz-box-shadow: 6px 6px 5px 0px rgba(0,0,0,0.60);
	box-shadow: 6px 6px 5px 0px rgba(0,0,0,0.60);
	
}

.chapterSubheading {
	text-align:left;
	font-weight:bold;
	font-size:140%;
	color:#0000A6;
}

.subheadingBlue {
	font-size:110%;
	font-weight:bold;
	color:#00aeef;
}

.bold {
	font-weight:bold;
}

.subheading {
	font-size:115%;
	font-weight:bold;
	color:#ff8000;
}

.chapterImage {
	height:100px;
	width:100px;
	float:right;
	margin-left:1%;
}
.popup {
	background-color:#DCF0F7;
	border-radius:15px;
    color:#00aeef;
	box-shadow: 0 5px 5px #777;
	margin-top:15px;
}
.blue_txt
{
	text-align:left;
	font-weight:bold;
	padding: 10px;
	color:#03F;
}
.newWordsBox{
background-color:#eaeaea;
padding: 15px;
font-size:0.9em;
border-top:3px solid #FFDE00;
}
.lining_box2
{
border:2px solid #FFDE00;
padding:15px;
border-radius:15px;
}
.line{

padding: 5px;
font-size:1.083em;
border-bottom:5px solid #FFDE00;
font-weight:bold;
margin-bottom: 5px; 
}
.clear
{
	clear:both;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
.chapter {
	text-align:left;
	font-weight:bold;
	font-size:130%;
	color:#C85C44;
}

.chapterNumber {
	text-align:left;
	font-weight:bold;
	font-size:180%;
	
	color:#d1640f;
	font-size:1.3em;
}

.chapterImage:after {
	clear:both;
}

.image {
	text-align: center;
}

.center {
	text-align: center;
	font-family: "Walkman-Chanakya-905";
}

.left {
	float:left;
}

.right {
	float:right;
}

.numbers {
	text-align:center;
	font-size:120%;
	font-weight:bold;
}

.englishMeaning
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:70%;
	}


div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.lining_box
{
background-color:#F4A460;
border:1px solid #000;
padding:5px;
}
.lining_box_1
{
background-color:#FFB2D1;
border:1px solid #000;
padding:5px;
}
.lining_box_2
{
background-color:#F5D6FF;
border:1px solid #000;
padding:5px;
}
.lining_box_3
{
background-color:#E0EBEB;
border:1px solid #000;
padding:5px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2

{

color:#006699;

}

h4

{

color:#d1640f;

}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:#87E293;
padding: 15px;
}
.box{
background-color:#DCF0F7;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.greenbox{
background-color:#99CC99;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}



body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
#prelims .char-style-override-23
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-7
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:50%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}