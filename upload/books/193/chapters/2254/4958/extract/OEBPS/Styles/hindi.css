@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

.underline_txt

{

font-decoration:underline;

}

.bold_txt

{

font-weight:bold;

}

.center_element

{

margin:auto;

}

.italics_txt

{

font-style:italic;

}

.block_element

{

display:block;

}

.img_rt

{

float:right;

clear:both;

}

.img_lft

{

float:left;

}
* {
margin:0;
padding:0;
}

.image {
text-align:center;
}
.author {
text-align:right;
}
h4
{
color:white;
font-size:1.5em;
background:#00aeef;
padding:10px;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
	font-size: 125%;
}

.subHeading {
color:#ce1337;
font-size:125%;
}

.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #999;
	background:#eaeaea;
}
p
{
	margin-top:10px;
}
h2
{
	color:#006699;
}

.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
.englishMeaning
{
	font-family:arial;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.clear{
clear:both;
}
.lining_box
{
border:2px solid #00aeef;
padding:15px;
border-radius:15px;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;font-family:"Walkman-Chanakya-905";
}

div.chapter_pos
{
  text-align: center;
  width: 96%;
  position:absolute;
	top:60%;
	font-weight:bold;
	font-size:28px;
	color:#fff;
}
div.chapter_pos div
{
	background:#003366;
	padding:10px;
	width:40%;
	margin:auto;
	opacity:0.9;
}
div.chapter_pos div span
{
	font-size:0.7em;
	color:#aeaeae;
	font-weight:normal;
}

@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:45%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;



}

.cover_img_small

{

width:90%;

}

}

#prelims .para-style-override-13, .para-style-override-24
{
	font-weight:bold;
}
#prelims .char-style-override-10, .heading
{
	font-size: 1.667em; 
	color: #008C44; 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-17
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#b55414;
}