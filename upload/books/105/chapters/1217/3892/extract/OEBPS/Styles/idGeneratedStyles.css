html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
.box1{
background-color:#e6e6e6;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
img
{
    max-width:100%;
}
.caption
{
    font-style: italic;
    font-size: 0.83em;
    color: #4D4D4D;
    text-align:center;
}
/* Chapter Name */
h1
{
color:#fff;
font-size:1.5em;
background:#FF8000;
padding:10px;
}
/* Chapter number */
h2
{
color:#3399ff;
font-size:1.3em;
margin-top:20px;
}
/* Concept Heading */
h3
{
color:#CC0000;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
h4
{
color:#000000;
font-size:1.1em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading 2*/
h5
{
color:#CC0000;
font-size:1.1em;
//font-weight:bold;
margin-top:20px;
}
.clear
{
    clear:both;
}
.box
{
border-top:2px solid #000000;
border-bottom:2px solid #000000;	
width: 20%;
margin-left:auto;
margin-right:auto;
}
.lining_box
{
border:2px solid #B3B3B6;
padding:15px;

}
.lining_box1
{
border:2px solid #000;
padding:2px 10px;
border-radius:15px;
}
.lining_box2
{
border:2px solid #2B2E34;
padding:5px;
width: 48px;

}
  p.resize img, .resize img
{

position:relative;
top:20px;
}
p.resize1 img, .resize1 img
{
position:relative;
top:15px;
}
p.resize2 img, .resize2 img, img.resize2
{

position:relative;
top:15px;
}
p.resize3 img, .resize3 img
{Sl. No.	Items	Major Head	Sub-head (if any)
height:75px;
position:relative;
top:25px;
} 
.ul
{
padding-left:15px;
}
/*font size small*/
.small
{
font-size:small;
}
.note
{
    font-style: italic;
    font-size: 0.83em;
    color: #4D4D4D;
}
p
{
    margin-top:10px;
}
table
{
 
margin-left: auto;
margin-right: auto;
 width:100%;
    border:1px solid #000;
    border-collapse:collapse;
    
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.cover_img_small
{
width:50%;
}
div.layout


{


text-align: center;


}


div.chapter_pos



{



text-align: center;



width: 96%;



position:absolute;



top:70%;



font-weight:bold;



font-size:28px;



color:#fff;



}



div.chapter_pos div



{



background:#B9881B;



padding:10px;



width:30%;



margin:auto;


opacity:0.9;



}



div.chapter_pos div span



{



font-size:0.7em;



color:#eaeaea;



font-weight:normal;



}


@media only screen and (max-width: 767px) {



div.chapter_pos



{



font-size:0.8em;


line-height:120%;


top:50%;


}



div.chapter_pos div span



{



font-size:0.7em;



}


}
div.layout


{


text-align: center;


}


div.chapter_pos



{



text-align: center;



width: 96%;



position:absolute;



top:50%;



font-weight:bold;



font-size:24px;



color:#fff;



}



div.chapter_pos div



{



background:#A5282C;



padding:10px;



width:32%;



margin:auto;


opacity:0.9;



}



div.chapter_pos div span



{



font-size:0.7em;



color:#eaeaea;



font-weight:normal;



}


@media only screen and (max-width: 767px) {



div.chapter_pos



{



font-size:0.8em;


line-height:120%;


top:40%;


}



div.chapter_pos div span



{



font-size:0.7em;



}


}






