<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 935 1320" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<style type="text/css"><![CDATA[
.g0_12{
fill: #FAD5E5;
}
.g1_12{
fill: none;
stroke: #ED028C;
stroke-width: 3.0555556;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g2_12{
fill: #ED028C;
stroke: #231F20;
stroke-width: 1.5277778;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g3_12{
fill: none;
stroke: #231F20;
stroke-width: 1.2222222;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g4_12{
fill: #231F20;
}
.g5_12{
fill: none;
stroke: #ED028C;
stroke-width: 0.73333335;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
.g6_12{
fill: none;
stroke: #ED028C;
stroke-width: 3.6666667;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 1000;
}
]]></style>
</defs>
<path d="M152.8,972.4h629V470.7h-629V972.4Z" class="g0_12" />
<path d="M781.8,470.7h-629V972.4h629V470.7Z" class="g1_12" />
<path fill-rule="evenodd" d="M468.1,668.8c50.8,0,92,19.5,92,43.5c0,24,-41.2,43.5,-92,43.5c-50.8,0,-92,-19.5,-92,-43.5c0,-24,41.2,-43.5,92,-43.5Z" class="g2_12" />
<path d="M515.4,673.8L549,600.5" class="g3_12" />
<path fill-rule="evenodd" d="M550.9,596.4l-5.4,5.5c2,-.2,3.6,.5,4.7,2.2l.7,-7.7Z" class="g4_12" />
<path d="M419.2,675.5l-64.1,-82" class="g3_12" />
<path fill-rule="evenodd" d="M352.3,589.9l2.3,7.3c.8,-1.9,2.2,-3,4.2,-3.2l-6.5,-4.1Z" class="g4_12" />
<path d="M385.7,694L341.3,659.1" class="g3_12" />
<path fill-rule="evenodd" d="M337.7,656.3l4.1,6.5c.2,-2,1.3,-3.4,3.2,-4.1l-7.3,-2.4Z" class="g4_12" />
<path d="M551.5,693.8l49.8,-42.2" class="g3_12" />
<path fill-rule="evenodd" d="M604.7,648.6l-7.2,2.7c1.9,.7,3.1,2,3.4,4l3.8,-6.7Z" class="g4_12" />
<path d="M553.6,729.9L573.8,740" class="g3_12" />
<path fill-rule="evenodd" d="M577.9,742.1l-5.3,-5.6c.1,2,-.7,3.6,-2.4,4.7l7.7,.9Z" class="g4_12" />
<path d="M516.8,749.2l33.6,34.3" class="g3_12" />
<path fill-rule="evenodd" d="M553.6,786.8l-3.2,-7c-.5,1.9,-1.8,3.2,-3.8,3.6l7,3.4Z" class="g4_12" />
<path d="M560.4,709.6l47.1,-.8" class="g3_12" />
<path fill-rule="evenodd" d="M612.1,708.7l-7.3,-2.5c1,1.8,1.1,3.5,0,5.3l7.3,-2.8Z" class="g4_12" />
<path d="M493.3,754.8l49.6,81.7" class="g3_12" />
<path fill-rule="evenodd" d="M545.2,840.4l-1.5,-7.5c-.9,1.8,-2.4,2.7,-4.5,2.7l6,4.8Z" class="g4_12" />
<path d="M480.6,754.8l51.8,129.5" class="g3_12" />
<path fill-rule="evenodd" d="M534,888.5l-.2,-7.7c-1.2,1.6,-2.9,2.3,-4.9,2l5.1,5.7Z" class="g4_12" />
<path d="M462.2,756.2V922.1" class="g3_12" />
<path fill-rule="evenodd" d="M462.2,926.7l2.6,-7.3c-1.8,1.1,-3.5,1.1,-5.3,0l2.7,7.3Z" class="g4_12" />
<path d="M399.1,857.4l43.2,-104" class="g3_12" />
<path fill-rule="evenodd" d="M397.4,861.6l.3,-7.7c1.2,1.6,2.9,2.3,4.9,2l-5.2,5.7Z" class="g4_12" />
<path d="M372.5,805.7l41.3,-56.4" class="g3_12" />
<path fill-rule="evenodd" d="M369.8,809.3l2.1,-7.4c.8,1.9,2.3,3,4.3,3.2l-6.4,4.2Z" class="g4_12" />
<path d="M348.2,764.2l40.5,-28.4" class="g3_12" />
<path fill-rule="evenodd" d="M344.4,766.8l4.5,-6.3c.1,2,1.1,3.4,3,4.3l-7.5,2Z" class="g4_12" />
<path d="M462.2,547.1v123" class="g3_12" />
<path fill-rule="evenodd" d="M462.2,542.5l2.6,7.3c-1.8,-1.1,-3.5,-1.1,-5.3,0l2.7,-7.3Z" class="g4_12" />
<path d="M330.3,709.5l47.1,-.8" class="g3_12" />
<path fill-rule="evenodd" d="M325.7,709.6l7.2,-2.8c-1,1.8,-1,3.6,.1,5.3l-7.3,-2.5Z" class="g4_12" />
<path d="M120.1,172.9H820.6v858H120.1v-858Z" class="g5_12" />
<path d="M116.1,169c1,-1,2.2,-1.5,3.6,-1.5H821c1.3,0,2.5,.5,3.5,1.5c1,1,1.5,2.2,1.5,3.6v858.7c0,1.3,-.5,2.5,-1.5,3.5c-1,1,-2.2,1.5,-3.5,1.5H119.7c-1.4,0,-2.6,-.5,-3.6,-1.5c-.9,-1,-1.4,-2.2,-1.4,-3.5V172.6c0,-1.4,.5,-2.6,1.4,-3.6Z" class="g6_12" />
</svg>