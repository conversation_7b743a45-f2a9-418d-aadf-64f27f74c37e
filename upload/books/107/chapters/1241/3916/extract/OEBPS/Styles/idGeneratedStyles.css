html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

img
{
max-width:100%;
}
/* Chapter Name */
h1
{
color:#FFFFFF;
font-size:1.5em;
padding:10px;
}
/* Chapter number */
h2
{
color:#9E2A96;
font-size:1.3em;
}
/* Concept Heading */
h3
{
color:#9E2A96;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
h4
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading 2*/
h5
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
.CharOverride-5{
vertical-align:sub;
font-size:smaller;
}
p
{
margin-top:10px;
}
table
{
width:100%;
border:1px solid #000;
border-collapse:collapse;
}
td
{
padding:10px;
border:1px solid #000;
border-collapse:collapse;
}
div.layout
{
text-align: center;
}
div.chapter_pos
{
text-align: center;
width: 96%;
position:absolute;
top:70%;
font-weight:bold;
font-size:28px;
color:#fff;
}
div.chapter_pos div
{
background:#839B47;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
font-size:0.7em;
color:#eaeaea;
font-weight:normal;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
font-size:0.8em;
line-height:120%;
top:50%;
}
div.chapter_pos div span
{
font-size:0.7em;
}
}