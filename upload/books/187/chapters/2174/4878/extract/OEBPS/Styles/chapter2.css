@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
html, body {
	font-family:"Walkman-Chanakya-905";
}

td, th {
	border-style:solid;
	border-width:1px;
}
table {
	border-collapse:collapse;
}
li {
	display:block;
}
body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}
@page {
	margin : 0px 0px 0px 0px;
}
p.No-Paragraph-Style {
	-epub-ruby-position:over;
	color:#000000;
	font-family:"Minion Pro", serif;
	font-size:1em;
	font-style:normal;
	font-variant:normal;
	font-weight:normal;
	line-height:1.2;
	margin-bottom:0px;
	margin-left:0px;
	margin-right:0px;
	text-align:left;
	text-decoration:none;
	text-indent:0px;
}
p.para-style-override-1 {
	margin-bottom:6px;
	text-align:center;
}
p.para-style-override-2 {
	margin-bottom:6px;
	text-align:right;
}
p.para-style-override-3 {
	text-align:justify;
}
p.para-style-override-4 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.167em;
	font-style:normal;
	font-weight:normal;
	text-align:center;
}
p.para-style-override-5 {
	text-align:justify;
	text-indent:24px;
}
p.para-style-override-6 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.333em;
	font-style:normal;
	font-weight:normal;
	text-align:justify;
	text-indent:24px;
}
span.char-style-override-1 {
	
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-2 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-3 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:italic;
	font-weight:normal;
}
span.char-style-override-4 {
	font-family:"Walkman-Chanakya-905", serif;
font-size:1em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-5 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-6 {
	color:#a72c32;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.3em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-7 {
	color:#a72c32;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-8 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-9{
font-family:"Walkman-Chanakya-905", serif;
	font-size:1em;
	font-style:normal;
	font-weight:normal;
}
img.frame-1 {
	height:50px;
	width:48px;
}
div.frame-2 {
	margin:0px auto 0px auto;
	text-align:center;
}
img.frame-3 {
	height:241px;
	width:393px;
}
img.frame-4 {
	height:126px;
	width:198px;
}
img.frame-5 {
	height:172px;
	width:164px;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
p
{
margin-top:10px;
}
/* Chapter Name */
h2
{
color:#FFFFFF;
font-size:1.5em;
background:#9A3334;
padding:10px;
}
/* Chapter number */
h4
{
color:#d1640f;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#d1640f;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#d1640f;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
.footer
{
display:none;
}
table td
{
padding:10px;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:#DCE4B1;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
/* Cover page band */
div.layout
{
  text-align: center;
}
div.chapter_pos
{
text-align: center;
width: 96%;
position:absolute;
top:50%;
line-height:110%;
font-weight:bold;
font-size:180%;
color:#fff;
}
div.chapter_pos div
{
background:#266A2E;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
font-size:0.7em;
color:#eaeaea;
font-weight:normal;
}
.cover_img_small
{
width:50%;
}
#prelims .char-style-override-27,  #prelims .para-style-override-20
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-13
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:80%;
}
.cover_img_small
{
width:90%;
}
}