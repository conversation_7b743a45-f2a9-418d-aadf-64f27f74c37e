        html, body, div, span,
        h1, h2, h3, h4, h5, h6, p, blockquote, pre,
        a, abbr, acronym, address, big, cite, code,
        del, dfn, em, img, ins, kbd, q, s, samp,
        small, strike, strong, sub, sup, tt, var,
        b, u, i, center,
        table, caption, tbody, tfoot, thead, tr, th, td,
        article, aside, canvas, details, embed,
        figure, figcaption, footer, header, hgroup,
        menu, nav, output, ruby, section, summary,
        time, mark, audio, video {
            margin: 0;
            padding: 0;
            border: 0;
            font-size: 100%;
            font: inherit;
            vertical-align: baseline;
        }
        body {line-height: 1;}
        a {color: inherit;}
        #bg {position: absolute;
                background-repeat: no-repeat;
                border: 0px;
                margin: 0px;
                z-index:-1;}
        .hid {z-index: -100;}
        .si {color: transparent;}
        .si::selection {
            background: #cc0000;
            color: transparent;
            opacity: 0.5;}
        .pos {position:absolute;
                /* white-space:pre; */
                -webkit-transform-origin: top left;}
        .hotspot {position: absolute; z-index: 100;}
        .hotspot a {position: relative; display: block; width: 100%;
                height: 100%;}
        .rot {white-space:nowrap;}
        .fit {position:absolute;
                text-align:justify !important;
                word-spacing:-10px;
                margin:0px;
                padding:0px;
                -webkit-transform-origin: top left;}
        .just:after {content:" ";
                display:inline-block;
                margin:0px;
                padding:0px;
                width:100%;
                height:0;
                visibility: hidden;}

        /* Audio Hotspots */
        .hot, .ibooks-media-audio:active {
                color: #ff4500 !important;
                border: none;
                background-color: #0066CC;
                opacity: 0.2;
                /* -moz-border-radius: 15px; */
                -webkit-border-radius: 15px;
                border-radius: 15px;}

         .hot audio {display:none; height: 1px !important; width: 1px !important; opacity: 0 !important; background-color: #fff;  }
         audio.hot  {display:none; height: 1px !important; width: 1px !important; opacity: 0 !important; background-color: #fff;  }

        /* Video */
        .video {
            display: block;
            position: absolute;}

        /* KF8 Panel View */
        .hide {display: none;}
        .lbwrap {display: none;
            height: 100%;
            width: 100%;
            position: absolute;}
        .lb {background-color: #FFDE9D;
            height: 100%;
            width: 100%;
            opacity: .8;
            position: absolute;}
        .target-mag {
            position: absolute;
            border: 5px solid #cdcdcd;
            border-radius: 8px;
            border-color: #8A4B08;
            padding: 5px;
            display: block;
            }
            
