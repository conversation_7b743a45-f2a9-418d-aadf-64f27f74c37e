
html, body {
font-family:"arial";
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
.Subhead2{
font-size:1.1em;
font-weight:bold;
margin-top:20px;
color:#F20A25;
}
.Subhead{
font-size:1.3em;
font-weight:bold;
margin-top:20px;
color:#AF879B;
}
.lining_box2
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.lining_box
{
border:2px solid red ; border-radius:15px; 
padding:15px;
}

.subheading {
color:#FF0000;
font-size:120%;
font-weight:bold;
}
p.iN-tHIS-CHAPTER {
	color:#fffcd5;
	font-size:2em;
}
p.para-style-override-2 {
	color:#000000;
}
p.Heading {
	color:#666000;
	font-size:2em;
}
span.char-style-override-2 {
	font-size:1.143em;
	font-weight:bold;
}
span.char-style-override-18 {
	font-style:italic;
}
p.para-style-override-15 {
	color:#000000;
	font-size:5em;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:1em;
line-height:120%;
}
p
{

margin-top:10px;

}

/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#F4D6BC;
padding:10px;
}
/* Chapter number */
h4
{
color:#d1640f;
font-size:1.5em;
}

.hd1
{
color:#fff;
font-size:2em;
font-weight:bold;
}
/* Concept Heading */
.ConceptHeading
{
color:#000;
font-size:1.2em;
font-weight:bold;
margin-top:20px;
}
.ConceptHeading1
{
color:#000;
font-size:1em;
margin-top:20px;
}
.credit
{
	display:none;
}

/*              NEW CSS           */



div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 97%;

position:absolute;

top:70%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#0e0e0e;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:100%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2

{

color:#006699;

}

h4

{

color:#d1640f;

}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
margin: 5px 5px 5px 5px;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
margin: 5px 5px 5px 5px;
}

.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:20%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

#prelims .char-style-override-15, #prelims .char-style-override-19
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#af7b0a;
}
.char-style-override-2
{
	font-style:italic;
}