<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title>Cover</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000001.jpg');"> </div>
    <div class="pos fs0" style="left: 187px; top: 204px; color: #231F20;">11</div>
    <div class="pos fs1" style="left: 255px; top: 201px; color: #231F20;">TablesandShares</div>
    <div class="pos fs2" style="left: 159px; top: 319px; color: #231F20;">Shyama's Garden</div>
    <div class="pos fit fs3" style="left: 158px; top: 400px; width: 434px; color: #231F20;">
      <span class="just">Shyama has planted sunflower,</span>
    </div>
    <div class="pos fit fs3" style="left: 158px; top: 435px; width: 434px; color: #231F20;">
      <span class="just">rose and marigold plants in her</span>
    </div>
    <div class="pos fit fs3" style="left: 158px; top: 470px; width: 434px; color: #231F20;">
      <span class="just">garden. She has planted them in</span>
    </div>
    <div class="pos fit fs3" style="left: 158px; top: 505px; width: 434px; color: #231F20;">
      <span class="just">three flower-beds. Her garden</span>
    </div>
    <div class="pos fs3" style="left: 158px; top: 540px; color: #231F20;">looks like this.</div>
    <div class="pos fs4" style="left: 386px; top: 644px; color: #231F20;">See,howIplanted</div>
    <div class="pos fs4" style="left: 398px; top: 675px; color: #231F20;">18plantsineach</div>
    <div class="pos fs4" style="left: 427px; top: 706px; color: #231F20;">flowerbed!</div>
    <div class="pos fs3" style="left: 161px; top: 849px; color: #231F20;">Each flower- bed has a different arrangement.</div>
    <div class="pos fs3" style="left: 161px; top: 900px; color: #231F20;" id="w1x">See</div>
    <div class="pos fs3" style="left: 212px; top: 900px; color: #231F20;" id="w2x">how</div>
    <div class="pos fs3" style="left: 272px; top: 900px; color: #231F20;" id="w3x">the</div>
    <div class="pos fs3" style="left: 329px; top: 900px; color: #231F20;" id="w4x">roses</div>
    <div class="pos fs3" style="left: 404px; top: 900px; color: #231F20;" id="w5x">are</div>
    <div class="pos fs3" style="left: 451px; top: 900px; color: #231F20;" id="w6x">planted.</div>
    <div class="pos fs3" style="left: 161px; top: 951px; color: #231F20;" id="w7x">18</div>
    <div class="pos fs3" style="left: 200px; top: 951px; color: #231F20;" id="w8x">=</div>
    <div class="pos fs3" style="left: 221px; top: 951px; color: #231F20;" id="w9x">6</div>
    <div class="pos fs3" style="left: 243px; top: 951px; color: #231F20;" id="w10x">×</div>
    <div class="pos fs3" style="left: 265px; top: 951px; color: #231F20;" id="w11x">3</div>
    <div class="pos fs3" style="left: 341px; top: 951px; color: #231F20;" id="w12x">So</div>
    <div class="pos fs3" style="left: 379px; top: 951px; color: #231F20;" id="w13x">there</div>
    <div class="pos fs3" style="left: 453px; top: 951px; color: #231F20;" id="w14x">are</div>
    <div class="pos fs3" style="left: 501px; top: 951px; color: #231F20;" id="w15x">6</div>
    <div class="pos fs3" style="left: 523px; top: 951px; color: #231F20;" id="w16x">rows</div>
    <div class="pos fs3" style="left: 591px; top: 951px; color: #231F20;" id="w17x">with</div>
    <div class="pos fs3" style="left: 654px; top: 951px; color: #231F20;" id="w18x">3</div>
    <div class="pos fs3" style="left: 676px; top: 951px; color: #231F20;" id="w19x">plants</div>
    <div class="pos fs3" style="left: 765px; top: 951px; color: #231F20;" id="w20x">each.</div>
    <div class="pos fs3" style="left: 161px; top: 1001px; color: #231F20;">What are the ways in which the sunflower and marigold are</div>
    <div class="pos fs3" style="left: 161px; top: 1036px; color: #231F20;">planted?</div>
    <div class="pos fs3" style="left: 161px; top: 1087px; color: #231F20;" id="w21x">18</div>
    <div class="pos fs3" style="left: 200px; top: 1087px; color: #231F20;" id="w22x">=</div>
    <div class="pos fs3" style="left: 221px; top: 1087px; color: #231F20;" id="w23x">_____</div>
    <div class="pos fs3" style="left: 295px; top: 1087px; color: #231F20;" id="w24x">×</div>
    <div class="pos fs3" style="left: 316px; top: 1087px; color: #231F20;" id="w25x">______</div>
    <div class="pos fs3" style="left: 441px; top: 1087px; color: #231F20;" id="w26x">So</div>
    <div class="pos fs3" style="left: 480px; top: 1087px; color: #231F20;" id="w27x">there</div>
    <div class="pos fs3" style="left: 554px; top: 1087px; color: #231F20;" id="w28x">is</div>
    <div class="pos fs3" style="left: 581px; top: 1087px; color: #231F20;" id="w29x">___</div>
    <div class="pos fs3" style="left: 627px; top: 1087px; color: #231F20;" id="w30x">row</div>
    <div class="pos fs3" style="left: 681px; top: 1087px; color: #231F20;" id="w31x">with</div>
    <div class="pos fs3" style="left: 744px; top: 1087px; color: #231F20;" id="w32x">___</div>
    <div class="pos fs3" style="left: 791px; top: 1087px; color: #231F20;" id="w33x">plants.</div>
    <div class="pos fs3" style="left: 161px; top: 1137px; color: #231F20;" id="w34x">18</div>
    <div class="pos fs3" style="left: 200px; top: 1137px; color: #231F20;" id="w35x">=</div>
    <div class="pos fs3" style="left: 221px; top: 1137px; color: #231F20;" id="w36x">_____</div>
    <div class="pos fs3" style="left: 295px; top: 1137px; color: #231F20;" id="w37x">×</div>
    <div class="pos fs3" style="left: 316px; top: 1137px; color: #231F20;" id="w38x">______</div>
    <div class="pos fs3" style="left: 441px; top: 1137px; color: #231F20;" id="w39x">So</div>
    <div class="pos fs3" style="left: 480px; top: 1137px; color: #231F20;" id="w40x">there</div>
    <div class="pos fs3" style="left: 554px; top: 1137px; color: #231F20;" id="w41x">are</div>
    <div class="pos fs3" style="left: 601px; top: 1137px; color: #231F20;" id="w42x">___</div>
    <div class="pos fs3" style="left: 647px; top: 1137px; color: #231F20;" id="w43x">rows</div>
    <div class="pos fs3" style="left: 715px; top: 1137px; color: #231F20;" id="w44x">with</div>
    <div class="pos fs3" style="left: 778px; top: 1137px; color: #231F20;" id="w45x">___</div>
    <div class="pos fs3" style="left: 825px; top: 1137px; color: #231F20;" id="w46x">plants</div>
    <div class="pos fs3" style="left: 913px; top: 1137px; color: #231F20;" id="w47x">each.</div>
    <div class="pos fit fs3" style="left: 161px; top: 1188px; width: 838px; color: #231F20;">
      <span class="just">You too can make your own garden. Draw a garden, showing</span>
    </div>
    <div class="pos fit fs3" style="left: 161px; top: 1223px; width: 838px; color: #231F20;">
      <span class="just">flower-beds with 48 plants. Each row should have the same</span>
    </div>
    <div class="pos fs3" style="left: 161px; top: 1258px; color: #231F20;">number of plants.</div>
    <div class="pos fit fs5" style="left: 205px; top: 1328px; width: 751px; color: #231F20;">
      <span class="just">The concept of multiplication can be related to the arrangement of things in an array. Some</span>
    </div>
    <div class="pos fit fs5" style="left: 205px; top: 1350px; width: 751px; color: #231F20;">
      <span class="just">other problems, based on contexts like the arrangement of chairs, children in the school</span>
    </div>
    <div class="pos fs5" style="left: 205px; top: 1372px; color: #231F20;">assembly, etc., can also be discussed.</div>
    <div class="pos fs6" style="left: 562px; top: 1432px; color: #231F20;">120</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000002.jpg');"> </div>
    <div class="pos fs2" style="left: 159px; top: 206px; color: #231F20;">Jars in the Shelf</div>
    <div class="pos fs3" style="left: 159px; top: 258px; color: #231F20;">Bheema made a shelf for 30 jars. This is a long shelf with two</div>
    <div class="pos fs3" style="left: 159px; top: 293px; color: #231F20;">rows. Each row has the samenumber of jars.</div>
    <div class="pos fs3" style="left: 158px; top: 608px; color: #231F20;">Can you think of other ways to make a shelf to keep 30 jars?</div>
    <div class="pos fs7" style="left: 158px; top: 659px; color: #EC008C;">8</div>
    <div class="pos fs3" style="left: 193px; top: 658px; color: #231F20;">Draw a shelf. Show how many jars you will keep in each</div>
    <div class="pos fs3" style="left: 193px; top: 693px; color: #231F20;">row. Howmany rows are there?</div>
    <div class="pos fs3" style="left: 193px; top: 744px; color: #231F20;">Have your friends drawn it in different ways?</div>
    <div class="pos fs2" style="left: 158px; top: 821px; color: #231F20;">Easy Tricks</div>
    <div class="pos fs4" style="left: 365px; top: 908px; color: #231F20;">Idonotknowthe</div>
    <div class="pos fs4" style="left: 357px; top: 943px; color: #231F20;">multiplicationtable</div>
    <div class="pos fs4" style="left: 436px; top: 978px; color: #231F20;">of7.</div>
    <div class="pos fs4" style="left: 641px; top: 1049px; color: #231F20;">Iknowthetablestill5but</div>
    <div class="pos fs4" style="left: 669px; top: 1078px; color: #231F20;">thereisaneasytrick.</div>
    <div class="pos fs4" style="left: 622px; top: 1117px; color: #231F20;">Icanmakethetableof7with</div>
    <div class="pos fs4" style="left: 670px; top: 1147px; color: #231F20;">thetablesof2and5.</div>
    <div class="pos fs8" style="left: 207px; top: 1189px; color: #231F20;">Bunty</div>
    <div class="pos fs8" style="left: 543px; top: 1263px; color: #231F20;">Guddu</div>
    <div class="pos fs5" style="left: 206px; top: 1348px; color: #231F20;">Children will enjoy building new multiplication tables for themselves instead of only</div>
    <div class="pos fs5" style="left: 206px; top: 1369px; color: #231F20;">memorising them.</div>
    <div class="pos fs6" style="left: 566px; top: 1432px; color: #231F20;">121</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000003.jpg');"> </div>
    <div class="pos fs3" style="left: 110px; top: 202px; color: #231F20;">Table of 2</div>
    <div class="pos fs9" style="left: 140px; top: 249px; color: #231F20;">1×2</div>
    <div class="pos fs10" style="left: 152px; top: 282px; color: #231F20;">2</div>
    <div class="pos fs3" style="left: 117px; top: 328px; color: #231F20;">Table of 5</div>
    <div class="pos fs9" style="left: 139px; top: 373px; color: #231F20;">1×5</div>
    <div class="pos fs10" style="left: 154px; top: 401px; color: #231F20;">5</div>
    <div class="pos fs3" style="left: 117px; top: 480px; color: #231F20;">Table of 7</div>
    <div class="pos fs11" style="left: 149px; top: 534px; color: #231F20;">7</div>
    <div class="pos fs9" style="left: 233px; top: 231px; color: #231F20;">2×2</div>
    <div class="pos fs10" style="left: 243px; top: 262px; color: #231F20;">4</div>
    <div class="pos fs9" style="left: 232px; top: 355px; color: #231F20;">2×5</div>
    <div class="pos fs10" style="left: 245px; top: 384px; color: #231F20;">10</div>
    <div class="pos fs10" style="left: 237px; top: 516px; color: #231F20;">14</div>
    <div class="pos fs9" style="left: 327px; top: 222px; color: #231F20;">3×2</div>
    <div class="pos fs10" style="left: 339px; top: 254px; color: #231F20;">6</div>
    <div class="pos fs9" style="left: 328px; top: 341px; color: #231F20;">3×5</div>
    <div class="pos fs10" style="left: 330px; top: 372px; color: #231F20;">15</div>
    <div class="pos fs10" style="left: 331px; top: 503px; color: #231F20;">21</div>
    <div class="pos fs4" style="left: 155px; top: 624px; color: #231F20;">See,howIaddedthe</div>
    <div class="pos fs4" style="left: 168px; top: 653px; color: #231F20;">twonumbersinthe</div>
    <div class="pos fs4" style="left: 167px; top: 682px; color: #231F20;">yellowboxestoget</div>
    <div class="pos fs4" style="left: 193px; top: 712px; color: #231F20;">thetableof7.</div>
    <div class="pos fs4" style="left: 833px; top: 742px; color: #231F20;">Aha...itiseasy.</div>
    <div class="pos fs4" style="left: 813px; top: 771px; color: #231F20;">Icanalsomakethe</div>
    <div class="pos fs4" style="left: 815px; top: 800px; color: #231F20;">tableof7withthe</div>
    <div class="pos fs4" style="left: 823px; top: 830px; color: #231F20;">tablesof4and3.</div>
    <div class="pos fs3" style="left: 116px; top: 982px; color: #231F20;">Table of 4</div>
    <div class="pos fs3" style="left: 158px; top: 1063px; color: #231F20;">4</div>
    <div class="pos fs3" style="left: 159px; top: 924px; color: #231F20;">Help Bunty to make the table of 7, using tables of 4 and 3.</div>
    <div class="pos fs12" style="left: 421px; top: 980px; color: #231F20;">4×4</div>
    <div class="pos fs3" style="left: 123px; top: 1108px; color: #231F20;">Table of 3</div>
    <div class="pos fs12" style="left: 140px; top: 1031px; color: #231F20;">1×4</div>
    <div class="pos fs13" style="left: 144px; top: 1152px; color: #231F20;">1×3</div>
    <div class="pos fs3" style="left: 158px; top: 1182px; color: #231F20;">3</div>
    <div class="pos fs3" style="left: 117px; top: 1247px; color: #231F20;">Table of 7</div>
    <div class="pos fs11" style="left: 149px; top: 1298px; color: #231F20;">7</div>
    <div class="pos fs3" style="left: 179px; top: 1372px; color: #231F20;">Which two tables will you use for writing the table of 12?</div>
    <div class="pos fs6" style="left: 563px; top: 1432px; color: #231F20;">122</div>
    <div class="pos fs12" style="left: 236px; top: 1002px; color: #231F20;">2×4 3×4</div>
    <div class="pos fs3" style="left: 248px; top: 1041px; color: #231F20;">8</div>
    <div class="pos fs13" style="left: 236px; top: 1135px; color: #231F20;">2×3</div>
    <div class="pos fs3" style="left: 249px; top: 1162px; color: #231F20;">6</div>
    <div class="pos fs13" style="left: 332px; top: 1123px; color: #231F20;">3×3</div>
    <div class="pos fs13" style="left: 424px; top: 1104px; color: #231F20;">4×3</div>
    <div class="pos fs12" style="left: 516px; top: 998px; color: #231F20;">5×4 6×4</div>
    <div class="pos fs13" style="left: 515px; top: 1122px; color: #231F20;">5×3</div>
    <div class="pos fs12" style="left: 704px; top: 1030px; color: #231F20;">7×4</div>
    <div class="pos fs13" style="left: 610px; top: 1134px; color: #231F20;">6×3</div>
    <div class="pos fs13" style="left: 705px; top: 1153px; color: #231F20;">7×3</div>
    <div class="pos fs12" style="left: 798px; top: 1014px; color: #231F20;">8×4</div>
    <div class="pos fs13" style="left: 799px; top: 1137px; color: #231F20;">8×3</div>
    <div class="pos fs9" style="left: 422px; top: 201px; color: #231F20;">4×2</div>
    <div class="pos fs10" style="left: 433px; top: 233px; color: #231F20;">8</div>
    <div class="pos fs9" style="left: 422px; top: 324px; color: #231F20;">4×5</div>
    <div class="pos fs10" style="left: 429px; top: 355px; color: #231F20;">20</div>
    <div class="pos fs10" style="left: 421px; top: 486px; color: #231F20;">28</div>
    <div class="pos fs10" style="left: 518px; top: 250px; color: #231F20;">10</div>
    <div class="pos fs9" style="left: 514px; top: 218px; color: #231F20;">5×2</div>
    <div class="pos fs10" style="left: 518px; top: 373px; color: #231F20;">25</div>
    <div class="pos fs9" style="left: 510px; top: 345px; color: #231F20;">5×5</div>
    <div class="pos fs10" style="left: 516px; top: 504px; color: #231F20;">35</div>
    <div class="pos fs10" style="left: 612px; top: 262px; color: #231F20;">12</div>
    <div class="pos fs9" style="left: 609px; top: 228px; color: #231F20;">6×2</div>
    <div class="pos fs10" style="left: 612px; top: 387px; color: #231F20;">30</div>
    <div class="pos fs9" style="left: 607px; top: 359px; color: #231F20;">6×5</div>
    <div class="pos fs10" style="left: 611px; top: 518px; color: #231F20;">42</div>
    <div class="pos fs10" style="left: 707px; top: 281px; color: #231F20;">14</div>
    <div class="pos fs9" style="left: 702px; top: 248px; color: #231F20;">7×2</div>
    <div class="pos fs10" style="left: 709px; top: 401px; color: #231F20;">35</div>
    <div class="pos fs9" style="left: 701px; top: 373px; color: #231F20;">7×5</div>
    <div class="pos fs10" style="left: 710px; top: 532px; color: #231F20;">49</div>
    <div class="pos fs10" style="left: 801px; top: 267px; color: #231F20;">16</div>
    <div class="pos fs9" style="left: 797px; top: 233px; color: #231F20;">8×2</div>
    <div class="pos fs10" style="left: 803px; top: 387px; color: #231F20;">40</div>
    <div class="pos fs9" style="left: 795px; top: 359px; color: #231F20;">8×5</div>
    <div class="pos fs10" style="left: 800px; top: 517px; color: #231F20;">56</div>
    <div class="pos fs10" style="left: 893px; top: 254px; color: #231F20;">18</div>
    <div class="pos fs9" style="left: 887px; top: 222px; color: #231F20;">9×2</div>
    <div class="pos fs10" style="left: 895px; top: 374px; color: #231F20;">45</div>
    <div class="pos fs9" style="left: 886px; top: 346px; color: #231F20;">9×5</div>
    <div class="pos fs10" style="left: 892px; top: 505px; color: #231F20;">63</div>
    <div class="pos fs9" style="left: 974px; top: 211px; color: #231F20;">10×2</div>
    <div class="pos fs10" style="left: 986px; top: 238px; color: #231F20;">20</div>
    <div class="pos fs9" style="left: 979px; top: 334px; color: #231F20;">10×5</div>
    <div class="pos fs10" style="left: 992px; top: 361px; color: #231F20;">50</div>
    <div class="pos fs10" style="left: 987px; top: 492px; color: #231F20;">70</div>
    <div class="pos fs12" style="left: 890px; top: 1001px; color: #231F20;">9×4</div>
    <div class="pos fs13" style="left: 891px; top: 1123px; color: #231F20;">9×3</div>
    <div class="pos fs12" style="left: 976px; top: 988px; color: #231F20;">10×4</div>
    <div class="pos fs13" style="left: 978px; top: 1111px; color: #231F20;">10×3</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000004.jpg');"> </div>
    <div class="pos fs2" style="left: 158px; top: 196px; color: #231F20;">How Many Cats?</div>
    <div class="pos fs3" style="left: 158px; top: 248px; color: #231F20;">Some of</div>
    <div class="pos fs3" style="left: 158px; top: 318px; color: #231F20;">many cats are there in the box?</div>
    <div class="pos fs3" style="left: 273px; top: 248px; color: #231F20;">Gayatri's cat</div>
    <div class="pos fs3" style="left: 440px; top: 248px; color: #231F20;" id="w1x">s</div>
    <div class="pos fs3" style="left: 463px; top: 248px; color: #231F20;" id="w2x">were</div>
    <div class="pos fs3" style="left: 533px; top: 248px; color: #231F20;" id="w3x">playing</div>
    <div class="pos fs3" style="left: 638px; top: 248px; color: #231F20;" id="w4x">in</div>
    <div class="pos fs3" style="left: 673px; top: 248px; color: #231F20;" id="w5x">a</div>
    <div class="pos fs3" style="left: 697px; top: 248px; color: #231F20;" id="w6x">box.</div>
    <div class="pos fs3" style="left: 762px; top: 248px; color: #231F20;" id="w7x">W</div>
    <div class="pos fs3" style="left: 902px; top: 248px; color: #231F20;" id="w8x">tried</div>
    <div class="pos fs3" style="left: 972px; top: 248px; color: #231F20;" id="w9x">to</div>
    <div class="pos fs3" style="left: 788px; top: 248px; color: #231F20;">hen she</div>
    <div class="pos fs3" style="left: 158px; top: 283px; color: #231F20;">count, all she could see were legs. She counted 28 legs. How</div>
    <div class="pos fs4" style="left: 555px; top: 779px; color: #231F20;">8legsmean2cats.</div>
    <div class="pos fs4" style="left: 555px; top: 810px; color: #231F20;">12legsmean_____cats.</div>
    <div class="pos fs3" style="left: 169px; top: 968px; color: #231F20;" id="w10x">Howmany</div>
    <div class="pos fs3" style="left: 312px; top: 968px; color: #231F20;" id="w11x">legs?</div>
    <div class="pos fs3" style="left: 449px; top: 968px; color: #231F20;" id="w12x">4</div>
    <div class="pos fs3" style="left: 519px; top: 968px; color: #231F20;" id="w13x">8</div>
    <div class="pos fs3" style="left: 592px; top: 968px; color: #231F20;" id="w14x">12</div>
    <div class="pos fs3" style="left: 169px; top: 1029px; color: #231F20;" id="w15x">Howmany</div>
    <div class="pos fs3" style="left: 312px; top: 1029px; color: #231F20;" id="w16x">cats?</div>
    <div class="pos fs3" style="left: 449px; top: 1029px; color: #231F20;" id="w17x">1</div>
    <div class="pos fs3" style="left: 519px; top: 1029px; color: #231F20;" id="w18x">2</div>
    <div class="pos fs3" style="left: 162px; top: 1091px; color: #231F20;">So 28 legs mean __________ cats.</div>
    <div class="pos fs3" style="left: 197px; top: 1142px; color: #231F20;">Billo has kept his chickens in a box. He counted 28 legs. How</div>
    <div class="pos fs3" style="left: 197px; top: 1177px; color: #231F20;">many chickens are there?</div>
    <div class="pos fs3" style="left: 197px; top: 1227px; color: #231F20;">Leela has not gone to school for 21 days. For how many weeks</div>
    <div class="pos fs3" style="left: 197px; top: 1262px; color: #231F20;">was she away from school?</div>
    <div class="pos fit fs5" style="left: 196px; top: 1324px; width: 771px; color: #231F20;">
      <span class="just">Encourage children to fill in the table and also proceed towards making generalisations. For</span>
    </div>
    <div class="pos fit fs5" style="left: 196px; top: 1345px; width: 771px; color: #231F20;">
      <span class="just">example, they should be able to see that 48 legs would mean there are 12 cats, or</span>
    </div>
    <div class="pos fs5" style="left: 196px; top: 1367px; color: #231F20;">vice versa. In fact, this forms the foundation for algebraic thinking in later years.</div>
    <div class="pos fs6" style="left: 563px; top: 1432px; color: #231F20;">123</div>
    <div class="pos fs7" style="left: 162px; top: 1143px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 162px; top: 1228px; color: #EC008C;">8</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000005.jpg');"> </div>
    <div class="pos fs2" style="left: 158px; top: 203px; color: #231F20;">Jumping Animals</div>
    <div class="pos fs3" style="left: 158px; top: 253px; color: #231F20;">Do you remember the jumping animals of Class III?</div>
    <div class="pos fs14" style="left: 186px; top: 301px; color: #231F20;">frog</div>
    <div class="pos fs3" style="left: 158px; top: 301px; color: #231F20;" id="w1x">A</div>
    <div class="pos fs3" style="left: 250px; top: 301px; color: #231F20;" id="w2x">jumps</div>
    <div class="pos fs3" style="left: 342px; top: 301px; color: #231F20;" id="w3x">3</div>
    <div class="pos fs3" style="left: 368px; top: 301px; color: #231F20;" id="w4x">steps</div>
    <div class="pos fs3" style="left: 446px; top: 301px; color: #231F20;" id="w5x">at</div>
    <div class="pos fs3" style="left: 481px; top: 301px; color: #231F20;" id="w6x">a</div>
    <div class="pos fs3" style="left: 506px; top: 301px; color: #231F20;" id="w7x">time</div>
    <div class="pos fs3" style="left: 573px; top: 301px; color: #231F20;" id="w8x">starting</div>
    <div class="pos fs3" style="left: 685px; top: 301px; color: #231F20;" id="w9x">from</div>
    <div class="pos fs3" style="left: 756px; top: 301px; color: #231F20;" id="w10x">0.</div>
    <div class="pos fs3" style="left: 193px; top: 350px; color: #231F20;">Count the jumps he takes to reach 27.</div>
    <div class="pos fs13" style="left: 203px; top: 456px; color: #231F20;">44</div>
    <div class="pos fs13" style="left: 180px; top: 488px; color: #231F20;">45</div>
    <div class="pos fs13" style="left: 175px; top: 526px; color: #231F20;">46</div>
    <div class="pos fs13" style="left: 186px; top: 561px; color: #231F20;">47</div>
    <div class="pos fs13" style="left: 94px; top: 609px; color: #231F20;">6059 58</div>
    <div class="pos fs13" style="left: 202px; top: 596px; color: #231F20;">48</div>
    <div class="pos fs13" style="left: 198px; top: 646px; color: #231F20;">57</div>
    <div class="pos fs13" style="left: 232px; top: 625px; color: #231F20;">49</div>
    <div class="pos fs13" style="left: 218px; top: 679px; color: #231F20;">56</div>
    <div class="pos fs13" style="left: 242px; top: 715px; color: #231F20;">55</div>
    <div class="pos fs13" style="left: 88px; top: 809px; color: #231F20;">6667</div>
    <div class="pos fs7" style="left: 157px; top: 861px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 157px; top: 907px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 157px; top: 1009px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 157px; top: 1055px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 157px; top: 1102px; color: #EC008C;">8</div>
    <div class="pos fs3" style="left: 192px; top: 860px; color: #231F20;">He has taken _________ jumps, if he is at 36.</div>
    <div class="pos fs3" style="left: 192px; top: 906px; color: #231F20;">If he is at 42, he has taken _______ jumps.</div>
    <div class="pos fs3 " style="left: 157px; top: 961px; color: #231F20;">
      <div class="just">Starting from 0, a<span style="font-family: 'bdcclh_bookman_demi';"> rabbit</span> jumps 5 steps at a time.</div>
    </div>
    <div class="pos fs3" style="left: 192px; top: 1008px; color: #231F20;">In how many jumps does he reach 25? ______________</div>
    <div class="pos fs3" style="left: 192px; top: 1054px; color: #231F20;">He reaches _________ after taking 8 jumps.</div>
    <div class="pos fs3" style="left: 192px; top: 1101px; color: #231F20;" id="w11x">He</div>
    <div class="pos fs3" style="left: 237px; top: 1101px; color: #231F20;" id="w12x">needs</div>
    <div class="pos fs3" style="left: 323px; top: 1101px; color: #231F20;" id="w13x">_________</div>
    <div class="pos fs3" style="left: 463px; top: 1101px; color: #231F20;" id="w14x">jumps</div>
    <div class="pos fs3" style="left: 555px; top: 1101px; color: #231F20;" id="w15x">to</div>
    <div class="pos fs3" style="left: 590px; top: 1101px; color: #231F20;" id="w16x">reach</div>
    <div class="pos fs3" style="left: 673px; top: 1101px; color: #231F20;" id="w17x">55.</div>
    <div class="pos fs2" style="left: 157px; top: 1162px; color: #231F20;">PracticeTime</div>
    <div class="pos fs3" style="left: 209px; top: 1210px; color: #231F20;" id="w18x">1)</div>
    <div class="pos fs3" style="left: 247px; top: 1210px; color: #231F20;" id="w19x">28</div>
    <div class="pos fs3" style="left: 289px; top: 1210px; color: #231F20;" id="w20x">÷</div>
    <div class="pos fs3" style="left: 315px; top: 1210px; color: #231F20;" id="w21x">2</div>
    <div class="pos fs3" style="left: 340px; top: 1210px; color: #231F20;" id="w22x">=</div>
    <div class="pos fs3" style="left: 209px; top: 1257px; color: #231F20;" id="w23x">3)</div>
    <div class="pos fs3" style="left: 247px; top: 1257px; color: #231F20;" id="w24x">48</div>
    <div class="pos fs3" style="left: 289px; top: 1257px; color: #231F20;" id="w25x">÷</div>
    <div class="pos fs3" style="left: 315px; top: 1257px; color: #231F20;" id="w26x">4</div>
    <div class="pos fs3" style="left: 340px; top: 1257px; color: #231F20;" id="w27x">=</div>
    <div class="pos fs3" style="left: 209px; top: 1304px; color: #231F20;" id="w28x">5)</div>
    <div class="pos fs3" style="left: 247px; top: 1304px; color: #231F20;" id="w29x">96</div>
    <div class="pos fs3" style="left: 289px; top: 1304px; color: #231F20;" id="w30x">÷</div>
    <div class="pos fs3" style="left: 315px; top: 1304px; color: #231F20;" id="w31x">8</div>
    <div class="pos fs3" style="left: 340px; top: 1304px; color: #231F20;" id="w32x">=</div>
    <div class="pos fs3" style="left: 508px; top: 1210px; color: #231F20;" id="w33x">2)</div>
    <div class="pos fs3" style="left: 556px; top: 1210px; color: #231F20;" id="w34x">56</div>
    <div class="pos fs3" style="left: 600px; top: 1210px; color: #231F20;" id="w35x">÷</div>
    <div class="pos fs3" style="left: 639px; top: 1210px; color: #231F20;" id="w36x">7</div>
    <div class="pos fs3" style="left: 669px; top: 1210px; color: #231F20;" id="w37x">=</div>
    <div class="pos fs3" style="left: 508px; top: 1257px; color: #231F20;" id="w38x">4)</div>
    <div class="pos fs3" style="left: 556px; top: 1257px; color: #231F20;" id="w39x">66</div>
    <div class="pos fs3" style="left: 600px; top: 1257px; color: #231F20;" id="w40x">÷</div>
    <div class="pos fs3" style="left: 639px; top: 1257px; color: #231F20;" id="w41x">6</div>
    <div class="pos fs3" style="left: 669px; top: 1257px; color: #231F20;" id="w42x">=</div>
    <div class="pos fs3" style="left: 508px; top: 1304px; color: #231F20;" id="w43x">6)</div>
    <div class="pos fs3" style="left: 539px; top: 1304px; color: #231F20;" id="w44x">110</div>
    <div class="pos fs3" style="left: 600px; top: 1304px; color: #231F20;" id="w45x">÷</div>
    <div class="pos fs3" style="left: 622px; top: 1304px; color: #231F20;" id="w46x">10</div>
    <div class="pos fs3" style="left: 669px; top: 1304px; color: #231F20;" id="w47x">=</div>
    <div class="pos fs5" style="left: 192px; top: 1353px; color: #231F20;">Children have done similar kinds of exercises for multiplication and division in Class III.</div>
    <div class="pos fs5" style="left: 192px; top: 1375px; color: #231F20;">Refer to pages 173-176, Math-Magic Class III, NCERT.</div>
    <div class="pos fs6" style="left: 564px; top: 1432px; color: #231F20;">124</div>
    <div class="pos fs13" style="left: 267px; top: 644px; color: #231F20;">50</div>
    <div class="pos fs13" style="left: 299px; top: 474px; color: #231F20;">41</div>
    <div class="pos fs13" style="left: 279px; top: 506px; color: #231F20;">40</div>
    <div class="pos fs13" style="left: 332px; top: 579px; color: #231F20;">37</div>
    <div class="pos fs13" style="left: 304px; top: 662px; color: #231F20;">51</div>
    <div class="pos fs13" style="left: 322px; top: 694px; color: #231F20;">52</div>
    <div class="pos fs13" style="left: 278px; top: 728px; color: #231F20;">54 53</div>
    <div class="pos fs13" style="left: 373px; top: 593px; color: #231F20;">36</div>
    <div class="pos fs13" style="left: 426px; top: 538px; color: #231F20;">26</div>
    <div class="pos fs13" style="left: 428px; top: 575px; color: #231F20;">27</div>
    <div class="pos fs13" style="left: 398px; top: 680px; color: #231F20;">34</div>
    <div class="pos fs13" style="left: 389px; top: 635px; color: #231F20;">35</div>
    <div class="pos fs13" style="left: 466px; top: 520px; color: #231F20;">25</div>
    <div class="pos fs13" style="left: 495px; top: 547px; color: #231F20;">24</div>
    <div class="pos fs13" style="left: 506px; top: 587px; color: #231F20;">23</div>
    <div class="pos fs13" style="left: 442px; top: 614px; color: #231F20;">28</div>
    <div class="pos fs13" style="left: 471px; top: 647px; color: #231F20;">29</div>
    <div class="pos fs13" style="left: 423px; top: 721px; color: #231F20;">33 32</div>
    <div class="pos fs13" style="left: 489px; top: 681px; color: #231F20;">30</div>
    <div class="pos fs13" style="left: 488px; top: 708px; color: #231F20;">31</div>
    <div class="pos fs13" style="left: 535px; top: 608px; color: #231F20;">22</div>
    <div class="pos fs13" style="left: 623px; top: 584px; color: #231F20;">20 19</div>
    <div class="pos fs13" style="left: 700px; top: 621px; color: #231F20;">18</div>
    <div class="pos fs13" style="left: 716px; top: 666px; color: #231F20;">17</div>
    <div class="pos fs13" style="left: 762px; top: 522px; color: #231F20;">10 9</div>
    <div class="pos fs13" style="left: 743px; top: 565px; color: #231F20;">11</div>
    <div class="pos fs13" style="left: 759px; top: 603px; color: #231F20;">12</div>
    <div class="pos fs13" style="left: 790px; top: 635px; color: #231F20;">13</div>
    <div class="pos fs13" style="left: 810px; top: 675px; color: #231F20;">14</div>
    <div class="pos fs13" style="left: 747px; top: 705px; color: #231F20;">16 15</div>
    <div class="pos fs13" style="left: 837px; top: 558px; color: #231F20;">8</div>
    <div class="pos fs13" style="left: 862px; top: 597px; color: #231F20;">7</div>
    <div class="pos fs13" style="left: 920px; top: 525px; color: #231F20;">2</div>
    <div class="pos fs13" style="left: 934px; top: 568px; color: #231F20;">3</div>
    <div class="pos fs13" style="left: 890px; top: 619px; color: #231F20;">6 5</div>
    <div class="pos fs13" style="left: 945px; top: 598px; color: #231F20;">4</div>
    <div class="pos fs13" style="left: 951px; top: 510px; color: #231F20;">1</div>
    <div class="pos fs7" style="left: 158px; top: 351px; color: #EC008C;">8</div>
    <div class="pos fs3" style="left: 158px; top: 393px; color: #231F20;">So, he has taken 27 ÷ 3 = _______ jumps.</div>
    <div class="pos fs13" style="left: 236px; top: 439px; color: #231F20;">43 42</div>
    <div class="pos fs13" style="left: 994px; top: 529px; color: #231F20;">0</div>
    <div class="pos fs13" style="left: 577px; top: 598px; -webkit-transform: rotate(-20.26deg); color: #231F20;">21</div>
    <div class="pos fs13" style="left: 296px; top: 575px; -webkit-transform: rotate(10.51deg); color: #231F20;">38</div>
    <div class="pos fs13" style="left: 274px; top: 532px; -webkit-transform: rotate(41.28deg); color: #231F20;">39</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000006.jpg');"> </div>
    <div class="pos fs2" style="left: 158px; top: 202px; color: #231F20;">SeaShells</div>
    <div class="pos fit fs3" style="left: 158px; top: 250px; width: 841px; color: #231F20;">
      <span class="just">Dhruv lives near the sea. He thought of making necklaces for his</span>
    </div>
    <div class="pos fit fs3" style="left: 158px; top: 285px; width: 841px; color: #231F20;">
      <span class="just">three friends. He looked for sea-shells the whole day. He collected</span>
    </div>
    <div class="pos fit fs3" style="left: 158px; top: 320px; width: 841px; color: #231F20;">
      <span class="just">112 sea-shells by evening. Now he had many different colourful</span>
    </div>
    <div class="pos fs3" style="left: 158px; top: 355px; color: #231F20;">and shiny shells.</div>
    <div class="pos fs4" style="left: 713px; top: 389px; color: #231F20;">Iwillmakeanecklaceof</div>
    <div class="pos fs4" style="left: 701px; top: 420px; color: #231F20;">28shells.Willtheseshells</div>
    <div class="pos fs4" style="left: 690px; top: 451px; color: #231F20;">beenoughtomakenecklaces</div>
    <div class="pos fs4" style="left: 745px; top: 482px; color: #231F20;">forallmyfriends?</div>
    <div class="pos fs3" style="left: 159px; top: 752px; color: #231F20;">He took 28 shells for one necklace.</div>
    <div class="pos fs3" style="left: 159px; top: 799px; color: #231F20;">112 – 28 = 84</div>
    <div class="pos fs3" style="left: 159px; top: 842px; color: #231F20;">Now he was left with 84 shells. Again he took 28 more shells for</div>
    <div class="pos fs3" style="left: 159px; top: 873px; color: #231F20;">the second necklace.</div>
    <div class="pos fit fs3" style="left: 194px; top: 916px; width: 502px; color: #231F20;">
      <span class="just">Howmany shells are left now?________</span>
    </div>
    <div class="pos fit fs3" style="left: 159px; top: 959px; width: 539px; color: #231F20;">
      <span class="just">Then he took shells for the third necklace.</span>
    </div>
    <div class="pos fs3" style="left: 194px; top: 1002px; color: #231F20;">So he was left with ________ shells.</div>
    <div class="pos fs3" style="left: 194px; top: 1044px; color: #231F20;">How many necklaces can Dhruv make from 112 shells?</div>
    <div class="pos fs3" style="left: 194px; top: 1076px; color: #231F20;">________</div>
    <div class="pos fs3" style="left: 194px; top: 1118px; color: #231F20;">Are the shells enough for making necklaces for all his friends?</div>
    <div class="pos fs3" style="left: 194px; top: 1150px; color: #231F20;">________</div>
    <div class="pos fs7" style="left: 159px; top: 917px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 159px; top: 1003px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 159px; top: 1045px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 159px; top: 1119px; color: #EC008C;">8</div>
    <div class="pos fs15" style="left: 159px; top: 1204px; color: #231F20;">Try these</div>
    <div class="pos fs3" style="left: 159px; top: 1251px; color: #231F20;">A) Kannu made a necklace of 17 sea-shells. How many such</div>
    <div class="pos fs3" style="left: 201px; top: 1282px; color: #231F20;" id="w1x">necklaces</div>
    <div class="pos fs3" style="left: 336px; top: 1282px; color: #231F20;" id="w2x">can</div>
    <div class="pos fs3" style="left: 389px; top: 1282px; color: #231F20;" id="w3x">be</div>
    <div class="pos fs3" style="left: 425px; top: 1282px; color: #231F20;" id="w4x">made</div>
    <div class="pos fs3" style="left: 503px; top: 1282px; color: #231F20;" id="w5x">using</div>
    <div class="pos fs3" style="left: 582px; top: 1282px; color: #231F20;" id="w6x">100</div>
    <div class="pos fs3" style="left: 647px; top: 1282px; color: #231F20;" id="w7x">sea-shells?</div>
    <div class="pos fit fs5" style="left: 194px; top: 1326px; width: 771px; color: #231F20;">
      <span class="just">Encourage children to solve questions based on division with large numbers, for which they</span>
    </div>
    <div class="pos fit fs5" style="left: 194px; top: 1348px; width: 771px; color: #231F20;">
      <span class="just">do not know multiplication tables, using repeated subtraction. More problems based on real</span>
    </div>
    <div class="pos fs5" style="left: 194px; top: 1369px; color: #231F20;">life contexts can be given.</div>
    <div class="pos fs6" style="left: 563px; top: 1432px; color: #231F20;">125</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000007.jpg');"> </div>
    <div class="pos fit fs3" style="left: 158px; top: 203px; width: 844px; color: #231F20;">
      <span class="just">B) One carton can hold 85 soap bars. Shally wants to pack</span>
    </div>
    <div class="pos fit fs3" style="left: 200px; top: 238px; width: 802px; color: #231F20;">
      <span class="just">338 soap bars. How many cartons does she need for packing</span>
    </div>
    <div class="pos fs3" style="left: 200px; top: 273px; color: #231F20;">all of them?</div>
    <div class="pos fs3" style="left: 158px; top: 331px; color: #231F20;">C)</div>
    <div class="pos fs3" style="left: 200px; top: 331px; color: #231F20;">Manpreet wants 1500 sacks of cement for making a house.</div>
    <div class="pos fs3" style="left: 200px; top: 366px; color: #231F20;">A truck carries 250 sacks at a time.</div>
    <div class="pos fs3" style="left: 200px; top: 402px; color: #231F20;">truck make?</div>
    <div class="pos fs3" style="left: 200px; top: 460px; color: #231F20;">A driver charges Rs 500 for a trip. ow much</div>
    <div class="pos fs3" style="left: 571px; top: 495px; color: #231F20;">?</div>
    <div class="pos fs3" style="left: 667px; top: 460px; color: #231F20;">H</div>
    <div class="pos fs3" style="left: 200px; top: 495px; color: #231F20;">pay the driver for all the trips</div>
    <div class="pos fs2" style="left: 158px; top: 572px; color: #231F20;">Gangu's Sweets</div>
    <div class="pos fs3 " style="left: 158px; top: 624px; color: #231F20;">
      <div class="just">Gangu is making sweets for Id. He has made a tray of 80<span style="font-family: 'whxech_bookman_lightitalic';"> laddoos</span>.</div>
    </div>
    <div class="pos fit fs3" style="left: 820px; top: 460px; width: 182px; color: #231F20;">
      <span class="just">will Manpreet</span>
    </div>
    <div class="pos fit fs3" style="left: 685px; top: 366px; width: 317px; color: #231F20;">
      <span class="just">How many trips will the</span>
    </div>
    <div class="pos fs4" style="left: 658px; top: 952px; color: #231F20;">Pleasepack4</div>
    <div class="pos fs4" style="left: 641px; top: 983px; color: #231F20;">laddoosinabox.</div>
    <div class="pos fs4" style="left: 647px; top: 1014px; color: #231F20;">Ineed23small</div>
    <div class="pos fs4" style="left: 696px; top: 1045px; color: #231F20;">boxes.</div>
    <div class="pos fs13" style="left: 924px; top: 1181px; color: #231F20;">Rabiya</div>
    <div class="pos fs7" style="left: 159px; top: 1226px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 159px; top: 1312px; color: #EC008C;">8</div>
    <div class="pos fs3" style="left: 193px; top: 1225px; color: #231F20;">Are the sweets in the tray enough to pack 23 small boxes?</div>
    <div class="pos fs3" style="left: 194px; top: 1260px; color: #231F20;">____________</div>
    <div class="pos fs3" style="left: 193px; top: 1311px; color: #231F20;">How many more sweets are needed? ______________</div>
    <div class="pos fs5" style="left: 195px; top: 1354px; color: #231F20;">For solving this problem, encourage children to use their own strategies—of making groups</div>
    <div class="pos fs5" style="left: 195px; top: 1376px; color: #231F20;">in the tray, using multiplication to do division or repeated subtraction, etc.</div>
    <div class="pos fs6" style="left: 563px; top: 1432px; color: #231F20;">126</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000008.jpg');"> </div>
    <div class="pos fs7" style="left: 158px; top: 205px; color: #EC008C;" id="w1x">8</div>
    <div class="pos fs3" style="left: 193px; top: 205px; color: #231F20;" id="w2x">Gangu</div>
    <div class="pos fs3" style="left: 290px; top: 205px; color: #231F20;" id="w3x">also</div>
    <div class="pos fs3" style="left: 353px; top: 205px; color: #231F20;" id="w4x">has</div>
    <div class="pos fs3" style="left: 409px; top: 205px; color: #231F20;" id="w5x">a</div>
    <div class="pos fs3" style="left: 434px; top: 205px; color: #231F20;" id="w6x">bigger</div>
    <div class="pos fs3" style="left: 523px; top: 205px; color: #231F20;" id="w7x">box</div>
    <div class="pos fs3" style="left: 579px; top: 205px; color: #231F20;" id="w8x">in</div>
    <div class="pos fs3" style="left: 614px; top: 205px; color: #231F20;" id="w9x">which</div>
    <div class="pos fs3" style="left: 703px; top: 205px; color: #231F20;" id="w10x">he</div>
    <div class="pos fs3" style="left: 744px; top: 205px; color: #231F20;" id="w11x">packs</div>
    <div class="pos fs3" style="left: 830px; top: 205px; color: #231F20;" id="w12x">12</div>
    <div class="pos fs16" style="left: 873px; top: 205px; color: #231F20;">l</div>
    <div class="pos fs16" style="left: 880px; top: 205px; color: #231F20;">a</div>
    <div class="pos fs16" style="left: 897px; top: 205px; color: #231F20;">d</div>
    <div class="pos fs16" style="left: 915px; top: 205px; color: #231F20;">d</div>
    <div class="pos fs16" style="left: 932px; top: 205px; color: #231F20;">o</div>
    <div class="pos fs16" style="left: 947px; top: 205px; color: #231F20;">o</div>
    <div class="pos fs16" style="left: 962px; top: 205px; color: #231F20;">s</div>
    <div class="pos fs3" style="left: 976px; top: 205px; color: #231F20;">.</div>
    <div class="pos fs3 " style="left: 193px; top: 240px; color: #231F20;">
      <div class="just">How many boxes does he need for packing 60<span style="font-family: 'whxech_bookman_lightitalic';"> laddoos</span>?</div>
    </div>
    <div class="pos fs2" style="left: 158px; top: 316px; color: #231F20;">PracticeTime</div>
    <div class="pos fs3" style="left: 158px; top: 365px; color: #231F20;" id="w13x">1)</div>
    <div class="pos fs3" style="left: 200px; top: 365px; color: #231F20;" id="w14x">Neelu</div>
    <div class="pos fs3" style="left: 282px; top: 365px; color: #231F20;" id="w15x">brought</div>
    <div class="pos fs3" style="left: 395px; top: 365px; color: #231F20;" id="w16x">15</div>
    <div class="pos fs3" style="left: 436px; top: 365px; color: #231F20;" id="w17x">storybooks</div>
    <div class="pos fs3" style="left: 588px; top: 365px; color: #231F20;" id="w18x">to</div>
    <div class="pos fs3" style="left: 621px; top: 365px; color: #231F20;" id="w19x">her</div>
    <div class="pos fs3" style="left: 672px; top: 365px; color: #231F20;" id="w20x">class.</div>
    <div class="pos fs3" style="left: 754px; top: 365px; color: #231F20;" id="w21x">Today</div>
    <div class="pos fs3" style="left: 882px; top: 365px; color: #231F20;" id="w22x">students</div>
    <div class="pos fs3" style="left: 200px; top: 396px; color: #231F20;">are present. Howmany children</div>
    <div class="pos fs3" style="left: 841px; top: 365px; color: #231F20;">45</div>
    <div class="pos fs3" style="left: 158px; top: 442px; color: #231F20;" id="w23x">2)</div>
    <div class="pos fs3" style="left: 200px; top: 442px; color: #231F20;" id="w24x">A</div>
    <div class="pos fs3" style="left: 230px; top: 442px; color: #231F20;" id="w25x">family</div>
    <div class="pos fs3" style="left: 324px; top: 442px; color: #231F20;" id="w26x">of</div>
    <div class="pos fs3" style="left: 360px; top: 442px; color: #231F20;" id="w27x">8</div>
    <div class="pos fs3" style="left: 389px; top: 442px; color: #231F20;" id="w28x">people</div>
    <div class="pos fs3" style="left: 486px; top: 442px; color: #231F20;" id="w29x">needs</div>
    <div class="pos fs3" style="left: 576px; top: 442px; color: #231F20;" id="w30x">60</div>
    <div class="pos fs3" style="left: 621px; top: 442px; color: #231F20;" id="w31x">kg</div>
    <div class="pos fs3" style="left: 665px; top: 442px; color: #231F20;" id="w32x">wheat</div>
    <div class="pos fs3" style="left: 757px; top: 442px; color: #231F20;" id="w33x">for</div>
    <div class="pos fs3" style="left: 805px; top: 442px; color: #231F20;" id="w34x">a</div>
    <div class="pos fs3" style="left: 832px; top: 442px; color: #231F20;" id="w35x">month.</div>
    <div class="pos fs3" style="left: 962px; top: 442px; color: #231F20;" id="w36x">ow</div>
    <div class="pos fs3" style="left: 200px; top: 474px; color: #231F20;">muchwheat does this family need for a week?</div>
    <div class="pos fs3" style="left: 620px; top: 396px; color: #231F20;">will need to share one book?</div>
    <div class="pos fs3" style="left: 940px; top: 442px; color: #231F20;">H</div>
    <div class="pos fs3" style="left: 158px; top: 520px; color: #231F20;" id="w37x">3)</div>
    <div class="pos fs3" style="left: 207px; top: 520px; color: #231F20;" id="w38x">Razia</div>
    <div class="pos fs3" style="left: 289px; top: 520px; color: #231F20;" id="w39x">wants</div>
    <div class="pos fs3" style="left: 377px; top: 520px; color: #231F20;" id="w40x">change</div>
    <div class="pos fs3" style="left: 481px; top: 520px; color: #231F20;" id="w41x">for</div>
    <div class="pos fs3" style="left: 525px; top: 520px; color: #231F20;" id="w42x">Rs</div>
    <div class="pos fs3" style="left: 568px; top: 520px; color: #231F20;" id="w43x">500.</div>
    <div class="pos fs3" style="left: 209px; top: 583px; color: #231F20;">How many notes will she get if she wants in return —</div>
    <div class="pos fs3" style="left: 210px; top: 633px; color: #231F20;" id="w44x">(a)</div>
    <div class="pos fs3" style="left: 280px; top: 633px; color: #231F20;" id="w45x">All</div>
    <div class="pos fs3" style="left: 320px; top: 633px; color: #231F20;" id="w46x">100</div>
    <div class="pos fs3" style="left: 376px; top: 633px; color: #231F20;" id="w47x">rupee</div>
    <div class="pos fs3" style="left: 457px; top: 633px; color: #231F20;" id="w48x">notes?</div>
    <div class="pos fs3" style="left: 549px; top: 633px; color: #231F20;" id="w49x">_________________</div>
    <div class="pos fs3" style="left: 210px; top: 684px; color: #231F20;" id="w50x">(b)</div>
    <div class="pos fs3" style="left: 280px; top: 684px; color: #231F20;" id="w51x">All</div>
    <div class="pos fs3" style="left: 320px; top: 684px; color: #231F20;" id="w52x">50</div>
    <div class="pos fs3" style="left: 359px; top: 684px; color: #231F20;" id="w53x">rupee</div>
    <div class="pos fs3" style="left: 440px; top: 684px; color: #231F20;" id="w54x">notes?</div>
    <div class="pos fs3" style="left: 532px; top: 684px; color: #231F20;" id="w55x">_________________</div>
    <div class="pos fs3" style="left: 210px; top: 735px; color: #231F20;" id="w56x">(c)</div>
    <div class="pos fs3" style="left: 280px; top: 735px; color: #231F20;" id="w57x">All</div>
    <div class="pos fs3" style="left: 320px; top: 735px; color: #231F20;" id="w58x">20</div>
    <div class="pos fs3" style="left: 359px; top: 735px; color: #231F20;" id="w59x">rupee</div>
    <div class="pos fs3" style="left: 440px; top: 735px; color: #231F20;" id="w60x">notes?</div>
    <div class="pos fs3" style="left: 532px; top: 735px; color: #231F20;" id="w61x">_________________</div>
    <div class="pos fs3" style="left: 210px; top: 785px; color: #231F20;" id="w62x">(d)</div>
    <div class="pos fs3" style="left: 280px; top: 785px; color: #231F20;" id="w63x">All</div>
    <div class="pos fs3" style="left: 320px; top: 785px; color: #231F20;" id="w64x">5</div>
    <div class="pos fs3" style="left: 342px; top: 785px; color: #231F20;" id="w65x">rupee</div>
    <div class="pos fs3" style="left: 423px; top: 785px; color: #231F20;" id="w66x">notes?</div>
    <div class="pos fs3" style="left: 515px; top: 785px; color: #231F20;" id="w67x">_________________</div>
    <div class="pos fs7" style="left: 158px; top: 837px; color: #EC008C;">8</div>
    <div class="pos fs7" style="left: 158px; top: 923px; color: #EC008C;">8</div>
    <div class="pos fs3" style="left: 194px; top: 836px; color: #231F20;">You have to distribute 72 tomatoes equally in 3 baskets. How</div>
    <div class="pos fs3" style="left: 193px; top: 871px; color: #231F20;">many tomatoes will there be in each?</div>
    <div class="pos fs3" style="left: 194px; top: 922px; color: #231F20;">There are 350 bricks in a hand-cart. Binod found the weight of</div>
    <div class="pos fs3" style="left: 193px; top: 957px; color: #231F20;">a brick to be 2 kg. What will be the weight of all the bricks?</div>
    <div class="pos fs6" style="left: 564px; top: 1432px; color: #231F20;">127</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000009.jpg');"> </div>
    <div class="pos fs2" style="left: 158px; top: 197px; color: #231F20;">Children and their Grandfather</div>
    <div class="pos fs3" style="left: 158px; top: 245px; color: #231F20;">Rashi, Seema, Mridul, Rohit and Lokesh asked their grandfather</div>
    <div class="pos fs3" style="left: 158px; top: 277px; color: #231F20;">to give them money for the Fair.</div>
    <div class="pos fs4" style="left: 536px; top: 362px; color: #231F20;">Ihave70rupeesinmypocket.</div>
    <div class="pos fs4" style="left: 552px; top: 394px; color: #231F20;">Tellmehowtosharemoney</div>
    <div class="pos fs4" style="left: 531px; top: 425px; color: #231F20;">equallyamongallofyou.Ifyou</div>
    <div class="pos fs4" style="left: 535px; top: 456px; color: #231F20;">areright,yougetthismoney!</div>
    <div class="pos fs2" style="left: 159px; top: 575px; color: #231F20;">One method</div>
    <div class="pos fs3" style="left: 159px; top: 623px; color: #231F20;">Rashi and Seema thought for a while and said—We know how to</div>
    <div class="pos fs3" style="left: 159px; top: 658px; color: #231F20;">do 70 ÷ 5.</div>
    <div class="pos fs3" style="left: 159px; top: 705px; color: #231F20;">Seema starts writing and says—</div>
    <div class="pos fs3" style="left: 279px; top: 750px; color: #EC008C;">10</div>
    <div class="pos fs3" style="left: 220px; top: 808px; color: #EC008C;">5</div>
    <div class="pos fs3" style="left: 279px; top: 904px; color: #2E3092;">20</div>
    <div class="pos fs3" style="left: 278px; top: 808px; color: #231F20;">70</div>
    <div class="pos fs3 fit " style="left: 256px; top: 852px; width: 56px; color: #231F20;">
      <div class="just">–<span style="color: #EC008C;"> 50</span></div>
    </div>
    <div class="pos fs3" style="left: 427px; top: 754px; color: #231F20;">First I give Rs 10 to each one of us.</div>
    <div class="pos fs3" style="left: 427px; top: 847px; color: #231F20;">So, I have distributed 5 × 10 = 50 rupees.</div>
    <div class="pos fs3" style="left: 427px; top: 894px; color: #231F20;">20 rupees are still left.</div>
    <div class="pos fs3" style="left: 160px; top: 998px; color: #231F20;">Rashi completes it like this. She says—</div>
    <div class="pos fs3" style="left: 831px; top: 1032px; color: #EC008C;">5</div>
    <div class="pos fs3" style="left: 160px; top: 1092px; color: #231F20;">I give 4 rupees more to each. So I have</div>
    <div class="pos fs3" style="left: 160px; top: 1123px; color: #231F20;">distributed 20 rupees.</div>
    <div class="pos fs3" style="left: 160px; top: 1170px; color: #231F20;">Now nothing is left. And all the money is divided</div>
    <div class="pos fs3" style="left: 160px; top: 1201px; color: #231F20;">equally.</div>
    <div class="pos fs3" style="left: 160px; top: 1247px; color: #231F20;">So, each gets 10 + 4 = 14 rupees.</div>
    <div class="pos fs3 " style="left: 862px; top: 996px; color: #EC008C;">
      <div class="just"><span style="color: #231F20;">10</span> + 4</div>
    </div>
    <div class="pos fit fs3" style="left: 866px; top: 1062px; width: 56px; color: #231F20;">
      <span class="just">– 50</span>
    </div>
    <div class="pos fs3 fit " style="left: 865px; top: 1127px; width: 56px; color: #231F20;">
      <div class="just">–<span style="color: #EC008C;"> 20</span></div>
    </div>
    <div class="pos fs3" style="left: 888px; top: 1031px; color: #231F20;">70</div>
    <div class="pos fs3" style="left: 889px; top: 1096px; color: #231F20;">20</div>
    <div class="pos fs3" style="left: 901px; top: 1168px; color: #231F20;">0</div>
    <div class="pos fit fs5" style="left: 195px; top: 1292px; width: 771px; color: #231F20;">
      <span class="just">This method is actually about how children divide when they distribute some objects</span>
    </div>
    <div class="pos fit fs5" style="left: 195px; top: 1314px; width: 771px; color: #231F20;">
      <span class="just">repeatedly. In this case, they might first give Rs 10 each to five people and then next</span>
    </div>
    <div class="pos fit fs5" style="left: 195px; top: 1336px; width: 771px; color: #231F20;">
      <span class="just">distribute the remaining money in the second round. They could as well distribute it by first</span>
    </div>
    <div class="pos fit fs5" style="left: 195px; top: 1357px; width: 771px; color: #231F20;">
      <span class="just">giving Rs 5 to each. Children can, thus, use any way to complete the process of division. This</span>
    </div>
    <div class="pos fs5" style="left: 195px; top: 1379px; color: #231F20;">is the beauty of this method.</div>
    <div class="pos fs6" style="left: 564px; top: 1432px; color: #231F20;">128</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000010.jpg');"> </div>
    <div class="pos fs2" style="left: 159px; top: 199px; color: #231F20;">Another Method</div>
    <div class="pos fs3" style="left: 159px; top: 247px; color: #231F20;">Mridul and Lokesh are trying 70 ÷ 5 in a different way.</div>
    <div class="pos fs3" style="left: 159px; top: 282px; color: #231F20;">Lokesh writes—</div>
    <div class="pos fs3" style="left: 159px; top: 317px; color: #231F20;">First, I give Rs 5 to each.</div>
    <div class="pos fs12" style="left: 755px; top: 363px; color: #EC008C;">5</div>
    <div class="pos fs3" style="left: 159px; top: 391px; color: #231F20;">I have distributed 5 × 5 = 25 rupees.</div>
    <div class="pos fs3" style="left: 159px; top: 430px; color: #231F20;">Next, I give Rs 6 more to every one.</div>
    <div class="pos fs3" style="left: 159px; top: 469px; color: #231F20;">I have distributed 30 rupees more.</div>
    <div class="pos fs3" style="left: 159px; top: 516px; color: #231F20;">Now I am left with ________ rupees.</div>
    <div class="pos fs12" style="left: 805px; top: 310px; color: #EC008C;">5 + 6</div>
    <div class="pos fs12" style="left: 814px; top: 361px; color: #231F20;">70</div>
    <div class="pos fit fs12" style="left: 792px; top: 396px; width: 56px; color: #231F20;">
      <span class="just">– 25</span>
    </div>
    <div class="pos fs12" style="left: 814px; top: 437px; color: #231F20;">45</div>
    <div class="pos fs12 fit " style="left: 791px; top: 473px; width: 56px; color: #231F20;">
      <div class="just">–<span style="color: #EC008C;"> 30</span></div>
    </div>
    <div class="pos fs3" style="left: 818px; top: 517px; color: #231F20;">?</div>
    <div class="pos fs3" style="left: 159px; top: 601px; color: #231F20;">How will Lokesh distribute the rest of the money? Complete it.</div>
    <div class="pos fs3" style="left: 159px; top: 648px; color: #231F20;" id="w1x">So,</div>
    <div class="pos fs3" style="left: 206px; top: 648px; color: #231F20;" id="w2x">each</div>
    <div class="pos fs3" style="left: 273px; top: 648px; color: #231F20;" id="w3x">child</div>
    <div class="pos fs3" style="left: 344px; top: 648px; color: #231F20;" id="w4x">gets</div>
    <div class="pos fs3" style="left: 403px; top: 648px; color: #231F20;" id="w5x">5</div>
    <div class="pos fs3" style="left: 425px; top: 648px; color: #231F20;" id="w6x">+</div>
    <div class="pos fs3" style="left: 446px; top: 648px; color: #231F20;" id="w7x">6</div>
    <div class="pos fs3" style="left: 468px; top: 648px; color: #231F20;" id="w8x">+</div>
    <div class="pos fs3" style="left: 490px; top: 648px; color: #231F20;" id="w9x">________</div>
    <div class="pos fs3" style="left: 604px; top: 648px; color: #231F20;" id="w10x">=</div>
    <div class="pos fs3" style="left: 635px; top: 648px; color: #231F20;" id="w11x">________</div>
    <div class="pos fs3" style="left: 749px; top: 648px; color: #231F20;" id="w12x">rupees.</div>
    <div class="pos fs4" style="left: 557px; top: 734px; color: #231F20;">Checkyouranswer!</div>
    <div class="pos fs4" style="left: 499px; top: 766px; color: #231F20;">Multiplyyouranswerby5and</div>
    <div class="pos fs4" style="left: 523px; top: 797px; color: #231F20;">seeifyouget70.Isyour</div>
    <div class="pos fs4" style="left: 572px; top: 828px; color: #231F20;">answercorrect?</div>
    <div class="pos fs2" style="left: 158px; top: 898px; color: #231F20;">Your Method</div>
    <div class="pos fs7" style="left: 158px; top: 948px; color: #EC008C;">8</div>
    <div class="pos fit fs3" style="left: 193px; top: 947px; width: 807px; color: #231F20;">
      <span class="just">Now use your own method to divide Rs 70 equally among</span>
    </div>
    <div class="pos fit fs3" style="left: 193px; top: 978px; width: 807px; color: #231F20;">
      <span class="just">5 people. If you want you can start by giving Rs 2 to each.</span>
    </div>
    <div class="pos fs3" style="left: 193px; top: 1009px; color: #231F20;">Or you can even start with Rs 11 to each.</div>
    <div class="pos fs4" style="left: 726px; top: 1072px; color: #231F20;">Canyoustartwith</div>
    <div class="pos fs4" style="left: 747px; top: 1103px; color: #231F20;">Rs15toeach?</div>
    <div class="pos fs2" style="left: 158px; top: 1148px; color: #231F20;">Try Doing These</div>
    <div class="pos fs3" style="left: 160px; top: 1227px; color: #231F20;">a)</div>
    <div class="pos fs3" style="left: 160px; top: 1324px; color: #231F20;">e)</div>
    <div class="pos fs3" style="left: 206px; top: 1227px; color: #231F20;">5 65</div>
    <div class="pos fs3" style="left: 207px; top: 1322px; color: #231F20;">4 72</div>
    <div class="pos fs3" style="left: 377px; top: 1225px; color: #231F20;" id="w13x">b)</div>
    <div class="pos fs3" style="left: 424px; top: 1225px; color: #231F20;" id="w14x">84</div>
    <div class="pos fs3" style="left: 467px; top: 1225px; color: #231F20;" id="w15x">÷</div>
    <div class="pos fs3" style="left: 492px; top: 1225px; color: #231F20;" id="w16x">2</div>
    <div class="pos fs3" style="left: 376px; top: 1321px; color: #231F20;" id="w17x">f</div>
    <div class="pos fs3" style="left: 394px; top: 1321px; color: #231F20;" id="w18x">)</div>
    <div class="pos fs3" style="left: 418px; top: 1321px; color: #231F20;" id="w19x">9</div>
    <div class="pos fs3" style="left: 461px; top: 1321px; color: #231F20;" id="w20x">108</div>
    <div class="pos fs6" style="left: 564px; top: 1432px; color: #231F20;">129</div>
    <div class="pos fs3" style="left: 625px; top: 1226px; color: #231F20;">c) 3 69</div>
    <div class="pos fs3" style="left: 625px; top: 1321px; color: #231F20;" id="w21x">g)</div>
    <div class="pos fs3" style="left: 676px; top: 1321px; color: #231F20;" id="w22x">232</div>
    <div class="pos fs3" style="left: 736px; top: 1321px; color: #231F20;" id="w23x">÷</div>
    <div class="pos fs3" style="left: 761px; top: 1321px; color: #231F20;" id="w24x">2</div>
    <div class="pos fs3" style="left: 844px; top: 1219px; color: #231F20;">d)</div>
    <div class="pos fs3" style="left: 844px; top: 1316px; color: #231F20;">h)</div>
    <div class="pos fs3" style="left: 908px; top: 1219px; color: #231F20;">90 ÷ 6</div>
    <div class="pos fs3" style="left: 904px; top: 1319px; color: #231F20;">2 428</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000011.jpg');"> </div>
    <div class="pos fs17" style="left: 158px; top: 206px; color: #231F20;">i)</div>
    <div class="pos fs17" style="left: 200px; top: 206px; color: #231F20;">Meera made 204 candles to sell in the market. She</div>
    <div class="pos fs17" style="left: 200px; top: 237px; color: #231F20;">makes packets of 6. How many packets will she make?</div>
    <div class="pos fs17" style="left: 200px; top: 284px; color: #231F20;">If she packs them in packets of 12, then how many</div>
    <div class="pos fs17" style="left: 200px; top: 315px; color: #231F20;">packets will she make?</div>
    <div class="pos fs17" style="left: 158px; top: 362px; color: #231F20;">j)</div>
    <div class="pos fs17" style="left: 200px; top: 362px; color: #231F20;" id="w1x">On</div>
    <div class="pos fs17" style="left: 250px; top: 362px; color: #231F20;" id="w2x">Sport</div>
    <div class="pos fs17" style="left: 348px; top: 362px; color: #231F20;" id="w3x">Day</div>
    <div class="pos fs17" style="left: 437px; top: 362px; color: #231F20;" id="w4x">61</div>
    <div class="pos fs17" style="left: 482px; top: 362px; color: #231F20;" id="w5x">children</div>
    <div class="pos fs17" style="left: 602px; top: 362px; color: #231F20;" id="w6x">are</div>
    <div class="pos fs17" style="left: 655px; top: 362px; color: #231F20;" id="w7x">in</div>
    <div class="pos fs17" style="left: 692px; top: 362px; color: #231F20;" id="w8x">the</div>
    <div class="pos fs17" style="left: 746px; top: 362px; color: #231F20;" id="w9x">school</div>
    <div class="pos fs17" style="left: 842px; top: 362px; color: #231F20;" id="w10x">playground.</div>
    <div class="pos fs17" style="left: 200px; top: 393px; color: #231F20;">They are</div>
    <div class="pos fs17" style="left: 323px; top: 362px; color: #231F20;">s</div>
    <div class="pos fs17" style="left: 401px; top: 362px; color: #231F20;">, 1</div>
    <div class="pos fs17" style="left: 327px; top: 393px; color: #231F20;" id="w11x">standing</div>
    <div class="pos fs17" style="left: 455px; top: 393px; color: #231F20;" id="w12x">in</div>
    <div class="pos fs17" style="left: 491px; top: 393px; color: #231F20;" id="w13x">7</div>
    <div class="pos fs17" style="left: 519px; top: 393px; color: #231F20;" id="w14x">equal</div>
    <div class="pos fs17" style="left: 602px; top: 393px; color: #231F20;" id="w15x">rows</div>
    <div class="pos fs17" style="left: 665px; top: 393px; color: #231F20;" id="w16x">.</div>
    <div class="pos fs17" style="left: 685px; top: 393px; color: #231F20;" id="w17x">How</div>
    <div class="pos fs17" style="left: 754px; top: 393px; color: #231F20;" id="w18x">many</div>
    <div class="pos fs17" style="left: 839px; top: 393px; color: #231F20;" id="w19x">children</div>
    <div class="pos fs17" style="left: 960px; top: 393px; color: #231F20;" id="w20x">are</div>
    <div class="pos fs17" style="left: 200px; top: 425px; color: #231F20;" id="w21x">there</div>
    <div class="pos fs17" style="left: 281px; top: 425px; color: #231F20;" id="w22x">in</div>
    <div class="pos fs17" style="left: 313px; top: 425px; color: #231F20;" id="w23x">each</div>
    <div class="pos fs17" style="left: 380px; top: 425px; color: #231F20;" id="w24x">row?</div>
    <div class="pos fs18" style="left: 158px; top: 466px; color: #231F20;">Story Problems</div>
    <div class="pos fs17" style="left: 158px; top: 519px; color: #231F20;">Srishti's grandma is asking her to make problems.</div>
    <div class="pos fs19" style="left: 428px; top: 697px; color: #231F20;" id="w25x">Look</div>
    <div class="pos fs19" style="left: 485px; top: 697px; color: #231F20;" id="w26x">at</div>
    <div class="pos fs19" style="left: 516px; top: 697px; color: #231F20;" id="w27x">the</div>
    <div class="pos fs19" style="left: 560px; top: 697px; color: #231F20;" id="w28x">picture</div>
    <div class="pos fs19" style="left: 653px; top: 697px; color: #231F20;" id="w29x">and</div>
    <div class="pos fs19" style="left: 442px; top: 732px; color: #231F20;">make a question on it.</div>
    <div class="pos fs19" style="left: 684px; top: 848px; color: #231F20;">There are 3 crates. Each</div>
    <div class="pos fs19" style="left: 680px; top: 883px; color: #231F20;">crate has 24 bottles in it.</div>
    <div class="pos fs20" style="left: 686px; top: 922px; color: #231F20;">My question: How many</div>
    <div class="pos fs20" style="left: 682px; top: 958px; color: #231F20;">bottles are there in all?</div>
    <div class="pos fs17" style="left: 158px; top: 1347px; color: #231F20;">Now you look at the other pictures and make questions like</div>
    <div class="pos fs17" style="left: 158px; top: 1378px; color: #231F20;">Srishti.</div>
    <div class="pos fs21" style="left: 562px; top: 1437px; color: #231F20;">130</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000012.jpg');"> </div>
    <div class="pos fs17" style="left: 177px; top: 191px; color: #231F20;">1.</div>
    <div class="pos fs17" style="left: 743px; top: 270px; color: #231F20;">There are 8 packets</div>
    <div class="pos fs22 " style="left: 743px; top: 297px; color: #231F20;">
      <div class="just"><span style="font-family: 'vxudim_bookman_light';">of</span> rakhis<span style="font-family: 'vxudim_bookman_light';">.</span></div>
    </div>
    <div class="pos fs17" style="left: 743px; top: 348px; color: #231F20;">Each packet has 6</div>
    <div class="pos fs17 " style="left: 743px; top: 376px; color: #231F20;">
      <div class="just"><span style="font-family: 'mpixwm_bookman_lightitalic';">rakhis</span> in it.</div>
    </div>
    <div class="pos fs17" style="left: 157px; top: 506px; color: #231F20;">Your question:</div>
    <div class="pos fs17" style="left: 175px; top: 572px; color: #231F20;">2.</div>
    <div class="pos fs17" style="left: 158px; top: 890px; color: #231F20;">There are 10 packets of sugar.</div>
    <div class="pos fs17" style="left: 158px; top: 936px; color: #231F20;">Saurabh paid 110 rupees for all the packets.</div>
    <div class="pos fs17" style="left: 158px; top: 991px; color: #231F20;">Your question:</div>
    <div class="pos fs17" style="left: 174px; top: 1081px; color: #231F20;">3.</div>
    <div class="pos fs17" style="left: 160px; top: 1273px; color: #231F20;" id="w1x">There</div>
    <div class="pos fs17" style="left: 250px; top: 1273px; color: #231F20;" id="w2x">are</div>
    <div class="pos fs17" style="left: 308px; top: 1273px; color: #231F20;" id="w3x">35</div>
    <div class="pos fs17" style="left: 356px; top: 1273px; color: #231F20;" id="w4x">students</div>
    <div class="pos fs17" style="left: 488px; top: 1273px; color: #231F20;" id="w5x">in</div>
    <div class="pos fs17" style="left: 529px; top: 1273px; color: #231F20;" id="w6x">7</div>
    <div class="pos fs17" style="left: 579px; top: 1273px; color: #231F20;" id="w7x">rows.</div>
    <div class="pos fs17" style="left: 666px; top: 1273px; color: #231F20;" id="w8x">Each</div>
    <div class="pos fs17" style="left: 748px; top: 1273px; color: #231F20;" id="w9x">row</div>
    <div class="pos fs17" style="left: 812px; top: 1273px; color: #231F20;" id="w10x">has</div>
    <div class="pos fs17" style="left: 875px; top: 1273px; color: #231F20;" id="w11x">the</div>
    <div class="pos fs17" style="left: 932px; top: 1273px; color: #231F20;" id="w12x">same</div>
    <div class="pos fs17" style="left: 160px; top: 1304px; color: #231F20;">number of students.</div>
    <div class="pos fs17" style="left: 158px; top: 1364px; color: #231F20;">Your question:</div>
    <div class="pos fs21" style="left: 566px; top: 1437px; color: #231F20;">131</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1159, height=1640"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1159px; height:1640px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1159px; height: 1640px; background-image: url('page_000013.jpg');"> </div>
    <div class="pos fs17" style="left: 157px; top: 204px; color: #231F20;">4. Hari, Seema, Chinku and Lakshmi are going to Guwahati.</div>
    <div class="pos fs17" style="left: 192px; top: 251px; color: #231F20;">The cost of one rail ticket is Rs 62.</div>
    <div class="pos fs17" style="left: 164px; top: 656px; color: #231F20;">Your question:</div>
    <div class="pos fs17" style="left: 157px; top: 759px; color: #231F20;">5. One metre of cloth costs Rs 20. Lalbiak bought some cloth and</div>
    <div class="pos fs17" style="left: 192px; top: 790px; color: #231F20;">paid Rs 140.</div>
    <div class="pos fs17" style="left: 157px; top: 1190px; color: #231F20;">Your question:</div>
    <div class="pos fs19" style="left: 742px; top: 1300px; color: #231F20;">Also guess the</div>
    <div class="pos fs19" style="left: 775px; top: 1332px; color: #231F20;">answers.</div>
    <div class="pos fs21" style="left: 563px; top: 1437px; color: #231F20;">132</div>
  </body>
</html>
