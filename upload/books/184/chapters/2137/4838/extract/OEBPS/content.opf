<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<package xmlns="http://www.idpf.org/2007/opf" unique-identifier="bookid" version="3.0">
  <metadata xmlns:dc="http://purl.org/dc/elements/1.1/">
    <dc:identifier id="bookid">urn:uuid:29d919dd-24f5-4384-be78-b447c9dc299b</dc:identifier>
    <dc:date xmlns:opf="http://www.idpf.org/2007/opf" opf:event="">2015-03-25</dc:date>
    <dc:language>en-GB</dc:language>
    <dc:date xmlns:opf="http://www.idpf.org/2007/opf" opf:event="modification">2015-09-21</dc:date>
    <meta content="Adobe InDesign" name="generator" />
    <meta property="dcterms:modified">2015-03-25T12:00:00Z</meta>
    <meta content="0.8.2" name="Sigil version" />
  </metadata>
  <manifest>
    <item href="Text/Chapter-3.xhtml" id="Chapter-3.xhtml" media-type="application/xhtml+xml" />
    <item href="toc.ncx" id="ncx" media-type="application/x-dtbncx+xml" />
    <item href="Styles/chapter3.css" id="chapter3.css" media-type="text/css" />
    <item href="Misc/Chanakya.PFB" id="Chanakya.PFB" media-type="application/x-font-type1" />
    <item href="Misc/ChanakyaBold.PFB" id="ChanakyaBold.PFB" media-type="application/x-font-type1" />
    <item href="Misc/Walkman-Chanakya905Bold.PFB" id="Walkman-Chanakya905Bold.PFB" media-type="application/x-font-type1" />
    <item href="Misc/Walkman-Chanakya905Normal.PFB" id="Walkman-Chanakya905Normal.PFB" media-type="application/x-font-type1" />
    <item href="Misc/Walkman-Chanakya905NormalItalic.PFB" id="Walkman-Chanakya905NormalItalic.PFB" media-type="application/x-font-type1" />
    <item href="Images/img1.jpeg" id="img1.jpeg" media-type="image/jpeg" />
    <item href="Images/img2.jpeg" id="img2.jpeg" media-type="image/jpeg" />
    <item href="Images/img3.jpeg" id="img3.jpeg" media-type="image/jpeg" />
    <item href="Images/img4.jpeg" id="img4.jpeg" media-type="image/jpeg" />
    <item href="Images/img5.png" id="img5.png" media-type="image/png" />
    <item href="Images/img10.jpeg" id="img10.jpeg" media-type="image/jpeg" />
    <item href="Images/img11.jpeg" id="img11.jpeg" media-type="image/jpeg" />
    <item href="Images/img12.jpeg" id="img12.jpeg" media-type="image/jpeg" />
    <item href="Images/img9.jpeg" id="img9.jpeg" media-type="image/jpeg" />
    <item href="Images/img14.png" id="img14.png" media-type="image/png" />
    <item href="Images/img15.jpeg" id="img15.jpeg" media-type="image/jpeg" />
    <item href="Images/img16.png" id="img16.png" media-type="image/png" />
    <item href="Images/img17.png" id="img17.png" media-type="image/png" />
    <item href="Images/img18.png" id="img18.png" media-type="image/png" />
    <item href="Images/img19.jpeg" id="img19.jpeg" media-type="image/jpeg" />
    <item href="Images/img20.jpeg" id="img20.jpeg" media-type="image/jpeg" />
    <item href="Images/img21.jpeg" id="img21.jpeg" media-type="image/jpeg" />
    <item href="Images/img22.jpeg" id="img22.jpeg" media-type="image/jpeg" />
    <item href="Fonts/wcni.ttf" id="wcni.ttf" media-type="application/x-font-ttf" />
    <item href="Fonts/wcn.ttf" id="wcn.ttf" media-type="application/x-font-ttf" />
    <item href="Fonts/wcbi.ttf" id="wcbi.ttf" media-type="application/x-font-ttf" />
    <item href="Fonts/wcb.ttf" id="wcb.ttf" media-type="application/x-font-ttf" />
    <item href="Images/img6.png" id="img6.png" media-type="image/png" />
    <item href="Images/img23.png" id="img23.png" media-type="image/png" />
    <item href="Images/img7.png" id="img7.png" media-type="image/png" />
    <item href="Images/img8.png" id="img8.png" media-type="image/png" />
    <item href="Text/Cover.xhtml" id="Cover.xhtml" media-type="application/xhtml+xml" />
    <item href="Images/img13.jpg" id="img13.jpg" media-type="image/jpeg" />
    <item href="Images/License.jpg" id="License.jpg" media-type="image/jpeg" />
    <item href="Images/cover1.jpg" id="cover1.jpg" media-type="image/jpeg" />
    <item href="Images/cover.png" id="cover.png" media-type="image/png" />
  </manifest>
  <spine toc="ncx">
    <itemref idref="Cover.xhtml" />
    <itemref idref="Chapter-3.xhtml" />
  </spine>
  <guide />
</package>
