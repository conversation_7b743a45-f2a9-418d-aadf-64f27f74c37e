@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}
body {
	font-size:120%;
	line-height:110%;
	padding:2%;
	text-align:left;
}

.chapterHeading {
	font-size:140%;
	font-weight:bold;
	color:#03C;
	
}

.chapterNumber {
	font-size:36px;
	font-weight:bold;
}

.subHeading {
	font-size:120%;
	color:#06C;
	margin-bottom:1%;
	font-weight:bold;
}
.meaning {
	font-size:95%;
}
.author {
	text-align:right;
	color: #00F;
}

.image {
text-align:center;
}

.activity {
	background: #BADBB6;
	padding:2%;
}
.activity2 {
	background:	#cce7d3;
		padding:2%;
}
.activity3 {
	background: #F8C1D9;
		padding:2%;
}
.englishMeaningblue{
	font-size:90%;
	font-weight:bold;
	font-family:Arial, Helvetica, sans-serif;
	color: #03C;
}
.blue{
	color: #00BAB7;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	color: #000;
	font-size:0.8em;
}
.bold
{
	font-size:100%;
color:#00BFFF;
font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}.chapterNumber {
	font-size: 120%;
	color: #F00;
	text-align:right;
}
.subHeading1 {
	font-size:120%;
	color: #C3C;
	margin-bottom:1%;
	font-weight:bold;
}
#A {
	color: #09F;
}
#a {
	color: #09F;
}
p .bold {
	color: #000;
}
.chapterNumber p {
	color: #000;
	text-align: left;
	font-size: 120%;
	font-family: Walkman-Chanakya-905;
}
#a {
	color: #06C;
}
#a {
	font-weight: normal;
}
B {
	font-size: 120%;
}
.superscript{
position:relative;
top:-10%;
font-size: 80%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
vertical-align:baseline;
bottom:-40%;
font-size: 80%;
font-family:Arial, Helvetica, sans-serif;
}
.author2 {
	text-align:right;
}

.line{
border-bottom:5px solid blue;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 95%;
position:absolute;

top:60%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#9A3334;

padding:10px;

width:40%;
line-height:120%;
margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}

table td

{

padding:10px;

}

.conc

{

color:#006699;

}
.img_wid

{

margin-left: auto;

margin-right: auto;

display: block;

width:80%;

}
body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
#prelims .para-style-override-18, .char-style-override-16, .char-style-override-26
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-19, .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
	.underline_txt
{
font-decoration:underline;
}
.bold_txt
{
font-weight:bold;
}
.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}




.cover_img_small
{
width:50%;
}

@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:30%;
font-size:1em;
}
div.chapter_pos div

{
width:80%;
}
.cover_img_small
{
width:90%;
}
}
	

table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}

#MathSc img
{
position:relative;
top:8px;
height:25px;
max-width:100%;

}


#MathSc img.medium, #MathSc .med img
{
height:45px;
top:18px;
margin-bottom:2px;

}
#MathSc .med3 img
{
height:50px;
top:18px;
margin-bottom:2px;

}
#MathSc .med2 img
{
height:30px;
top:10px;
margin-bottom:2px;

}
#MathSc p img.small, #MathSc p.small img
{
position:relative;
top:10px;
height:30px;
max-width:100%;

}
#MathSc p img.small2
{
position:relative;
top:10px;
height:25px;
max-width:100%;

}
#MathSc p img.rupee
{
position:relative;
top:3px;
height:12px;

}
#MathSc .high
{
height:auto; 
width:auto;
}
#MathSc .image img, #MathSc .caption img, #MathSc img.image, #MathSc p.normal img
{
height:auto;
position:relative;
top:20px;
}

h2
{
color:#1414E0;
font-size:1.5em;
background:#8ED8F8;
padding:10px;
}
/* Chapter number */
h4
{
color:#FF0000;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#d1640f;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#00BFFF;
font-size:1.3em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#00BFFF;
font-size:1.1em;
font-weight:bold;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.lining_box
{
border:3px solid #BADBB6;
padding:15px;
}