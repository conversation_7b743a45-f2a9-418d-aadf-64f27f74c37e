<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Linear Programming</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">It is an important optimisation (maximisation or minimisation) technique used in decision making business and everyday life for obtaining the maximum or minimum values as required of a linear expression by satisfying certain number of given linear restrictions.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:14px"><strong>Linear Programming Problem (LPP)</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">The linear programming problem in general calls for optimising alinear funtion ofvariables called the objective function subject to a set oflinear equations and/or linear inequations called the constraints or restrictions.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:14px"><strong>Objective Function</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">The function which is to be optimised (maximised/minimised) is called an objective function.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:14px"><strong>Constraints</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">The system oflinear inequations or equations under which the objective function is to be optimised is called constraints.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:14px"><strong>Non-negative Restrictions</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">All the variables considered for making decisions assume non-negative values.</span></span></p>

<p style="text-align:justify">&nbsp;</p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Mathematical Description of a General Linear Programming Problem</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">A general LPP can be stated as</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span class="math-tex">\((\displaystyle \max/\min)Z=c_{1}{x}_{1}+c_{2}{x}_{2}+ +c_{n}{x}_{n}\)</span> (Objective function) subject to constraints</span></span></p>

<p style="text-align:justify"><br />
<span style="font-family:Helvetica"><span style="color:#4e5f70"><span class="math-tex">\(\left\{\begin{array}{lll}
    a_{11}x_1+a_{12}x_2+...+a_{1n}x_n(\leq=\geq)b_1           \\[0.3em]
   a_{21}x_1+a_{22}x_2+...+a_{2n}x_n(\leq=\geq)b_2 \\ \vdots\ \ \ \ \ \   \ \ \ \vdots\ \ \ \ \ \ \ \ \ \vdots\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \vdots\ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \ \vdots
\\ a_{m1}x_1+a_{m2}x_2+...+a_{mn}x_n(\leq= \geq)b_m

     
     \end{array}\right\}\)</span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">and the non-negative restrictions</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span class="math-tex">\(x_{1},\ x_{2},..., x_{n}\geq 0\)</span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">where, all <span class="math-tex">\(a_{11}, a_{12}... a_{mn};b_{1}, b_{2}... b_{m};c_{1}, c_{2}... c_{n}\)</span> are constants and <span class="math-tex">\( {x}_{1}, {x}_{2} {x}_{n}\)</span> are variables.</span></span></p>

<p style="text-align:justify">&nbsp;</p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Slack and Surplus Variables</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">The positive variables which are added to left hand sides of the constraints to convert them into equalities, are called the slack variables. The positive variables which are subtracted from the left hand sides of the constraints to convert them into equalities, are called the surplus variables.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:14px"><strong>Important Definitions and Results</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>&nbsp;Solution of an LPP </strong>A set of values of the variables<span class="math-tex">\( {x}_{1},{x}_{2}, {x}_{{n}}\)</span> satisfying the constraints of an LPP is called a solution of the LPP.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>&nbsp;Feasible Solution of an LPP</strong> A set of values of the variables <span class="math-tex">\({x}_{1}, {x}_{2} {x}_{{n}} \)</span>satisfying the constraints and non-negative restriction of an LPP is called afeasible solution of the LPP.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>Optimal Solution of an LPP</strong> A feasible solution of an LPP is said to be optimal or optimum, if it also optimises the objective function of the problem.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>Graphical Solution of an LPP </strong>The solution of an LPP obtained by graphical method i.e. by drawing the graphs corresponding to the constraints and the non-negative restrictions is called the graphical solution of an LPP.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>&nbsp;Unbounded Solution</strong> If the value of the objective function can be increased or decreased indefinitely, such solutions are called unbounded solutions.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>&nbsp;Fundamental Extreme</strong> Point Theorem An optimum solution of an LPP, if it exists, occurs at one of the extreme points (i.e. corner points) of the convex polygon of the set of all feasible solutions.</span></span></p>

<p style="text-align:justify">&nbsp;</p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:16px"><strong>Graphical Method of Solving Linear Programming Problem</strong></span></span></span></p>

<p style="text-align:justify">&nbsp;</p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">The graphical method is suitable for solving linear programming problems containing two variables only. This method of solving linear programming problem is referred as corner point method. The procedure of this method is as follows:</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">1. Find the feasible region of the LPP and determine its corner points (vertices) either by inspection or by solving the two equations of the lines intersecting at that point.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">2. Find the value of objective function Z=ax+by at each corner point. Let M and m respectively denote the largest and the smallest values of these points.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">(i) When the feasible region is bounded, M and m are the maximum and minimum values of Z.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">(ii) When the feasible region is unbounded, then</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">(a) M is the maximum value of Z, if the open half plane determined by ax+by&gt;M has no point in common with the feasible region. Otherwise, Z has no maximum value.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">(b) m is the minimum value of Z, if the open half plane determined by ax+by&lt;m has no point in common with the feasible region. Otherwise, Z has no minimum value.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>NOTE</strong> A balf plane ( if aline divides XY- plane into two parts then each part is known as balf plane) in} {X}{Y}- plane is called an open half plane, if the line separating the half plane is not included in the half plane.</span></span></p>

<p style="text-align:justify">&nbsp;</p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><span style="font-size:14px"><strong>Method to Solve LPP</strong></span></span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">To solve a Linear Programming Problem (LPP) we use the following working steps:</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">&nbsp;<strong>Step I</strong> Firstly, write the given LPP in mathematical form by using mathematical formulation (if not given in mathematical form).</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70">&nbsp;<strong>Step II</strong> Consider all constrains as linear equations.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>&nbsp;Step III</strong> Draw the graph ofeach linear equation obtained in Step II and find their intersection points (if any).<br />
<br />
<strong>Step IV</strong> Shade the common region of all the linear inequalities i.e. find feasible region and check that feasible region is bounded or unbounded.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>Step V</strong> Now, find the corner points of feasible region and calculate the value of objective function at each corner point.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>Step VI</strong> (i) If region is bounded, then maximum (say M) or minimum (say m) value out of these values obtained in </span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>Step V</strong>, is the required maximum or minimum value of the objective function. (ii) If region is unbounded, no need to go next step. Then, maximum (say M) or minimum (say m) value out of these values obtained in Step V may or may not be required maximum or minimum value of the objective function. Then, we go to next step.</span></span></p>

<p style="text-align:justify"><span style="font-family:Helvetica"><span style="color:#4e5f70"><strong>Step VII</strong> Suppose the given objective function is ax+by, then draw the graph of inequality ax+by&gt;M or ax+by&lt;m. If open half plane obtained by these inequalities has no point in common with the feasible region obtained in Step IV, then M or m is the required maximum or minimum value. Otherwise, objective function has no maximum or no minimum value.</span></span></p>

<p style="text-align:justify">&nbsp;</p>
