@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}
body {
	font-size:120%;
	line-height:150%;
	text-align:justify;
}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
.white_border table, .white_border td
{
 border:1px solid #fff;
}
.blue_border table, .blue_border td
{
border:1px solid rgb(0,255,255);
}
.brown_border table, .brown_border td
{
border:1px solid #996600;
}
.brown_color
{
background-color:#E1D194;
padding:10px;
}
.dark_brown
{
color:#fff;
font-size:1.3em;
background:#996600;
padding:8px;
}
.orange
{
font-weight:bold;
color:rgb(254,103,0);
}
.lining_box1
{
border:1px solid #fff;
padding:5px;
}
/* Hightlisght Boxes */
.box{
background-color:#E1D194;
padding: 15px;
line-height:120%;
}
.NewWordBox{
background-color:#E1D194;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.NewWordBox1{
background-color:#B3FFFF;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:#E1D194;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox1{
background-color:#F8C1D9;
padding: 15px;
line-height:120%;
border:2px solid rgb(147,149,152);
}
.activityBox2{
background-color:#cce7d3;
padding: 15px;
line-height:120%;
border:2px solid rgb(0,166,80);
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:rgb(0,204,0);
padding:10px;
}
/* Chapter number */
h4
{
color:rgb(0,204,0);
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:rgb(0,204,0);
font-size:1.3em;
font-weight:bold;

}
/* Sub Heading */
.SubHeading
{
color:#666666;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
.clear
{
	clear:both;
}
.shabad
{
color:rgb(255,51,255);
font-size:1.3em;
}
.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}

.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
.block_element
{
display:block;
}
.englishMeaning
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:0.8em;
	}
.img_rt
{
float:right;
clear:both;
}

.img_lft
{
float:left;
clear:both;
}
.line{
border-bottom:2px solid rgb(0,255,255);
}

p.para-style-override-1 {
	color:rgb(255,51,255);
}
p.para-style-override-2 {
	color:#000;
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}

p.para-style-override-4 {
	text-align:justify;
	text-indent:18px;
}
p.para-style-override-5 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}

p.para-style-override-7 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
p.para-style-override-8 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:normal;
	font-weight:normal;
}
p.para-style-override-9 {
	color:#00aeef;
font-weight:bold;
}
p.para-style-override-10 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
p.para-style-override-11 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
p.para-style-override-12 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
p.para-style-override-13 {
	text-align:center;
}
p.para-style-override-14 {
	color:#ecdfc2;
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
p.para-style-override-15 {
	margin-bottom:6px;
}
p.para-style-override-16 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}

p.para-style-override-18 {
	color:#ffffff;
	font-family:"Walkman-Chanakya-905", serif;
}
p.para-style-override-19 {
	color:#000000;
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}
p.para-style-override-20 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}

p.para-style-override-22 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}
p.para-style-override-23 {
	font-family:"Walkman-Chanakya-905", serif;
}
p.para-style-override-24 {
	font-family:"Walkman-Chanakya-905", serif;
}
p.para-style-override-25 {
	font-family:"Walkman-Chanakya-905", serif;
	}

p.para-style-override-27 {
	color:#ef5ba1;
	font-family:"Walkman-Chanakya-905", serif;
}
span.char-style-override-1 {
	font-family:"Walkman-Chanakya-905", serif;
	
}
span.char-style-override-2 {
	font-family:"Bookman-LightItalic", serif;
	
	font-style:italic;
	font-weight:bold;
}
span.char-style-override-3 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:italic;
	font-weight:bold;
}
span.char-style-override-4 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-5 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:italic;
	font-weight:normal;
}
span.char-style-override-6 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:italic;
	font-weight:normal;
}
span.char-style-override-7 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-8 {
	font-family:Chanakya;

	font-style:normal;
	font-weight:normal;
}


span.char-style-override-13 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:italic;
	font-weight:normal;
}



span.char-style-override-17 {
	color:#00aeef;
}

span.char-style-override-19 {
	font-family:Chanakya;
	
}
span.char-style-override-20 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-21 {
	color:#000000;
	font-family:"Walkman-Chanakya-905", serif;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-22 {
	font-family:Chanakya, serif;
	font-style:normal;
	font-weight:normal;
}

.blue_text {
	color:#00aeef;
	font-weight:bold;
}
span.char-style-override-25 {
	color:#00aeef;
	font-weight:bold;
}

span.char-style-override-27 {
	font-family:"Bookman-Light";
	
	font-weight:500;
}
span.char-style-override-28 {
	color:#00aeef;
	font-family:Chanakya, serif;
	
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-29 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-30 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.091em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-31 {
	font-size:0.963em;
}
span.char-style-override-32 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-33 {
	font-family:Bookman, serif;
	
	font-style:normal;
	font-weight:600;
}
span.char-style-override-34 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:italic;
}
span.char-style-override-35 {
	font-family:"Walkman-Chanakya-905", serif;
}
span.char-style-override-36 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-weight:bold;
}
span.char-style-override-37 {
	color:#ec008c;
	font-family:"Walkman-Chanakya-905", serif;
	}
span.char-style-override-38 {
	font-family:"Walkman-Chanakya-905", serif;
}
span.char-style-override-39 {
	color:#ec008c;
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-40 {
	color:#00aeef;
	font-family:"Walkman-Chanakya-905", serif;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-41 {
	color:#ffffff;
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-42 {
	color:#ffffff;
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-43 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}
span.char-style-override-44 {
	font-family:"Walkman-Chanakya-905", serif;
	font-style:italic;
}
span.char-style-override-45 {
	color:#00aeef;
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:bold;
}
span.char-style-override-46 {
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
span.char-style-override-47 {
	color:#ef5ba1;
	font-family:"Walkman-Chanakya-905", serif;
	font-weight:normal;
}
span.char-style-override-48 {
	color:#00aeef;
	font-weight:bold;
}
img{
max-width:100%;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	width:50%;
}

ul li
{
	list-style-type:bullet;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.cover_img_small

{

width:50%;

}
#prelims .char-style-override-25
{
	font-weight:bold;
	color:#000;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
#prelims .char-style-override-27, #prelims .char-style-override-8 {
	font-family:"Walkman-Chanakya-905";
	
	font-weight:normal;
}
#prelims .char-style-override-19 {
	font-family:"Walkman-Chanakya-905";
	
}
#prelims .para-style-override-4 {
	text-align:justify;
	text-indent:0px;
}
#prelims .char-style-override-3 {
	font-family:"Walkman-Chanakya-905", serif;
	
	font-style:italic;
	font-weight:normal;
}
@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:20%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;



}

.cover_img_small

{

width:90%;

}
}