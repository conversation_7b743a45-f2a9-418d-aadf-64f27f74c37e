<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 909 1286" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<clipPath id="c0_26"><path d="M614.9,143.3l0,-21l237.4,0l0,21Z" /></clipPath>
<style type="text/css"><![CDATA[
.g1_26{
fill: #231F20;
}
.g2_26{
fill: none;
stroke: #231F20;
stroke-width: 0.5253105;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g3_26{
fill: none;
stroke: #231F20;
stroke-width: 0.23862301;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g4_26{
fill: none;
stroke: #A8AAAD;
stroke-width: 0.9168042;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g5_26{
fill: #A8AAAD;
}
.g6_26{
fill: none;
stroke: #939598;
stroke-width: 0.9168042;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
]]></style>
</defs>
<path fill-rule="evenodd" d="M267.5,411.4l37.4,0l-37.4,0Z" class="g1_26" />
<path d="M267.5,411.4l37.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M248.6,530.3l37.4,0l-37.4,0Z" class="g1_26" />
<path d="M248.6,530.3l37.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M275.8,662.9l37.5,0l-37.5,0Z" class="g1_26" />
<path d="M275.8,662.9l37.5,0" class="g2_26" />
<path fill-rule="evenodd" d="M270.6,900.6l67.5,0l-67.5,0Z" class="g1_26" />
<path d="M270.6,900.6l67.5,0" class="g2_26" />
<path fill-rule="evenodd" d="M654.3,204.2l76.7,0l-76.7,0Z" class="g1_26" />
<path d="M654.3,204.2l76.7,0" class="g2_26" />
<path fill-rule="evenodd" d="M663.9,243.6l17.1,0l-17.1,0Z" class="g1_26" />
<path d="M663.9,243.6l17.1,0" class="g3_26" />
<path fill-rule="evenodd" d="M748.5,243.6l17,0l-17,0Z" class="g1_26" />
<path d="M748.5,243.6l17,0" class="g3_26" />
<path fill-rule="evenodd" d="M707.9,279.5l9.1,0l-9.1,0Z" class="g1_26" />
<path d="M707.9,279.5l9.1,0" class="g3_26" />
<path fill-rule="evenodd" d="M656,262.1l124.3,0l-124.3,0Z" class="g1_26" />
<path d="M656,262.1l124.3,0" class="g2_26" />
<path fill-rule="evenodd" d="M796.4,262.1l27.8,0l-27.8,0Z" class="g1_26" />
<path d="M796.4,262.1l27.8,0" class="g2_26" />
<path fill-rule="evenodd" d="M651.4,584.9l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M651.4,584.9l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M690.1,584.9l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M690.1,584.9l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M728.9,584.9l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M728.9,584.9l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M760.8,584.9l16.4,0l-16.4,0Z" class="g1_26" />
<path d="M760.8,584.9l16.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M566.3,626l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M566.3,626l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M593.7,626l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M593.7,626l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M621.1,626l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M621.1,626l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M647.4,626l95.4,0l-95.4,0Z" class="g1_26" />
<path d="M647.4,626l95.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M758.8,626l16.4,0l-16.4,0Z" class="g1_26" />
<path d="M758.8,626l16.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M721.5,697.5l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M721.5,697.5l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M748.9,697.5l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M748.9,697.5l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M775.2,697.5l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M775.2,697.5l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M531.2,730.8l16.4,0l-16.4,0Z" class="g1_26" />
<path d="M531.2,730.8l16.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M540.8,765.6l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M540.8,765.6l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M560.1,765.6l10.3,0l-10.3,0Z" class="g1_26" />
<path d="M560.1,765.6l10.3,0" class="g2_26" />
<path fill-rule="evenodd" d="M579.5,765.6l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M579.5,765.6l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M715.7,765.6l16.4,0l-16.4,0Z" class="g1_26" />
<path d="M715.7,765.6l16.4,0" class="g2_26" />
<path fill-rule="evenodd" d="M559.3,975.3l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M559.3,975.3l10.2,0" class="g2_26" />
<path fill-rule="evenodd" d="M559.3,1026.6l10.2,0l-10.2,0Z" class="g1_26" />
<path d="M559.3,1026.6l10.2,0" class="g2_26" />
<path d="M55.7,150.4l797.5,0" class="g4_26" />
<path d="M734.6,123.2l0,6.4l-0.8,0c-0.6,-2,-1.3,-3.5,-2.4,-4.4c-1,-0.9,-2.5,-1.3,-4.3,-1.3l-1.1,0l0,7.9c1.2,0,2,-0.2,2.6,-0.8c0.6,-0.6,1,-1.6,1.2,-2.8l0.8,0l0,8.1l-0.8,0c-0.2,-1.3,-0.6,-2.2,-1.2,-2.8c-0.6,-0.6,-1.4,-0.9,-2.6,-0.9l0,8.3l1.5,0c1.7,0,3.1,-0.5,4.2,-1.6c1.1,-1,1.9,-2.6,2.4,-4.7l0.9,0l0,7.1l-16.2,0l0,-0.8l2.1,0l0,-17l-2.1,0l0,-0.7l15.8,0Z" class="g1_26" />
<g clip-path="url(#c0_26)">
<path d="M708.9,123.9l-4.3,0l0,-0.8l12.4,0l0,0.8l-2.3,0l0,11.6c0,2.3,-0.6,4.7,-1.7,5.8c-3.6,3.3,-13.9,3,-12.7,-3.7c0.5,-3.2,5.5,-4,5.5,-0.5c0,0.5,-0.1,0.9,-0.3,1.3c-0.2,0.4,-0.5,0.7,-0.9,0.9c-0.1,0,-0.4,0.1,-0.8,0.2c-0.4,0.1,-0.6,0.3,-0.6,0.5c0,0.2,0.1,0.3,0.4,0.4c0.2,0.2,0.4,0.2,0.7,0.2c2.6,0,8.4,-3,4.6,-5.9l0,-10.8Z" class="g1_26" />
</g>
<path d="M754,123.2l0,6.4l-0.9,0c-0.5,-2,-1.3,-3.5,-2.3,-4.4c-1,-0.9,-2.5,-1.3,-4.3,-1.3l-1.2,0l0,7.9l0.1,0c1.1,0,2,-0.2,2.6,-0.8c0.6,-0.6,1,-1.6,1.1,-2.8l0.8,0l0,8.1l-0.8,0C749,135,748.6,134.1,748,133.5c-0.6,-0.6,-1.5,-0.9,-2.6,-0.9l-0.1,0l0,8.3l1.5,0c1.8,0,3.2,-0.5,4.2,-1.6c1.1,-1,2,-2.6,2.5,-4.7l0.9,0l0,7.1l-16.2,0l0,-0.8l2.1,0l0,-17l-2.1,0l0,-0.7l15.8,0Z" class="g1_26" />
<path d="M136,209.3c-0.4,0,-0.8,0.1,-1,0.4c-0.3,0.3,-0.5,0.6,-0.5,1c0,0.4,0.2,0.8,0.5,1c0.2,0.3,0.6,0.5,1,0.5c0.4,0,0.7,-0.2,1,-0.5c0.3,-0.2,0.4,-0.6,0.4,-1c0,-0.4,-0.1,-0.7,-0.4,-1c-0.3,-0.3,-0.6,-0.4,-1,-0.4Z" class="g1_26" />
<path d="M142.6,196.5c0,-1.8,-0.7,-3.4,-2,-4.6c-1.2,-1.3,-3.2,-2,-5.6,-2l0,1.8c1.9,0,3.5,0.4,4.4,1.4c1,0.9,1.5,2.1,1.5,3.4c0,1.4,-0.5,2.5,-1.5,3.5l-2.4,2.2c-1.2,1.3,-1.9,2.8,-1.9,4.6c0,0.3,0.1,0.5,0.3,0.6c0.1,0.2,0.3,0.3,0.6,0.3c0.2,0,0.4,-0.1,0.6,-0.3c0.2,-0.1,0.2,-0.3,0.2,-0.6c0,-1.3,0.5,-2.5,1.5,-3.4l2.3,-2.2c1.3,-1.3,2,-2.8,2,-4.7Z" class="g1_26" />
<path d="M126.7,205c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0,-0.2,0.1,-0.3l1.6,-1.7c0.1,-0.1,0.2,-0.1,0.3,-0.1c0.2,0,0.3,0,0.4,0.1c0,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.2,-0.1,0.3l-1.7,1.7l-0.1,-0.2l0.1,0.2c-0.1,0.1,-0.2,0.1,-0.3,0.1c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm0.8,-0.8l0,0l-0.8,0.8l0.8,-0.8Z" class="g5_26" />
<path d="M124.3,202.4c0,0,-0.1,-0.1,-0.1,-0.2c0,-0.2,0.1,-0.3,0.2,-0.4l1.9,-1.4c0.1,0,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0.1,0.3,0.2c0.1,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.2,-0.2,0.3l-1.9,1.4l-0.1,-0.2l0.1,0.2c0,0.1,-0.1,0.1,-0.2,0.1c-0.2,0,-0.3,-0.1,-0.4,-0.2Zm0.4,-0.2l0,0Zm0,0l0,0Zm1.9,-1.4l0,0Z" class="g5_26" />
<path d="M122.5,198.6l0,-0.1c0,-0.2,0.2,-0.3,0.4,-0.4l2.3,-0.5c0.2,0,0.4,0.2,0.5,0.4l0,0.1c0,0.2,-0.2,0.4,-0.4,0.4L123,199l0,-0.2l0,0.2c-0.2,0,-0.4,-0.2,-0.5,-0.4Zm0.5,-0.1l0,0Zm2.2,-0.4l0,0Z" class="g5_26" />
<path d="M125.2,195.4L122.9,195l0.1,-0.2l-0.1,0.2c-0.2,0,-0.4,-0.2,-0.4,-0.4c0,0,0.1,0,0.1,-0.1c0,-0.2,0.2,-0.3,0.4,-0.3l0.1,0l2.3,0.4c0.2,0,0.3,0.2,0.3,0.4l0,0.1c0,0.2,-0.2,0.3,-0.4,0.3c0,0,-0.1,0,-0.1,0Zm0.1,-0.4l0,0Zm-2.4,0l0,0Z" class="g5_26" />
<path d="M126,192.6l-2,-1.2l0.1,-0.1l-0.1,0.1c-0.2,0,-0.3,-0.2,-0.3,-0.3c0,-0.1,0.1,-0.2,0.1,-0.2c0.1,-0.2,0.2,-0.3,0.4,-0.3c0,0,0.1,0.1,0.2,0.1l2,1.2c0.1,0,0.2,0.2,0.2,0.3c0,0.1,0,0.2,0,0.3c-0.1,0.1,-0.3,0.2,-0.4,0.2c-0.1,0,-0.2,0,-0.2,-0.1Zm0.2,-0.4l0,0Zm-1.4,-0.8l0,0l-0.6,-0.3l0.6,0.3Z" class="g5_26" />
<path d="M128.1,190.5c-0.1,0,-0.3,-0.1,-0.3,-0.2l-1.4,-1.9l0.2,-0.2l-0.2,0.2c0,-0.1,0,-0.2,0,-0.3c0,-0.1,0,-0.3,0.1,-0.4c0.1,0,0.2,0,0.3,0c0.1,0,0.3,0,0.3,0.2l1.4,1.9c0,0.1,0,0.2,0,0.2c0,0.2,0,0.3,-0.1,0.4c-0.1,0,-0.2,0.1,-0.3,0.1Zm0,-0.5l0,0l-1.3,-1.9l1.3,1.9Zm-1.3,-1.9l0,0Z" class="g5_26" />
<path d="M130.5,189L130,186.7c0,0,0,-0.1,0,-0.1c0,-0.2,0.1,-0.4,0.3,-0.4c0.1,0,0.1,0,0.1,0c0.2,0,0.4,0.1,0.5,0.3l0.5,2.3c0,0,0,0.1,0,0.1c0,0.2,-0.2,0.4,-0.4,0.4l-0.1,0c-0.2,0,-0.3,-0.1,-0.4,-0.3Zm0.4,-0.1l0,0Zm0,0l0,0Zm-0.7,-2.2l0,0l0.2,-0.1l-0.2,0.1Z" class="g5_26" />
<path d="M126.4,196.5c0,-1.8,0.7,-3.4,2,-4.6c1.2,-1.3,3.2,-2,5.6,-2l0,1.8c-1.9,0,-3.5,0.4,-4.4,1.4c-1,0.9,-1.5,2.1,-1.5,3.4c0,1.4,0.5,2.5,1.5,3.5l2.4,2.2c1.2,1.3,1.9,2.8,1.9,4.6c0,0.3,-0.1,0.5,-0.3,0.6c-0.1,0.2,-0.3,0.3,-0.6,0.3c-0.2,0,-0.4,-0.1,-0.6,-0.3c-0.2,-0.1,-0.2,-0.3,-0.2,-0.6c0,-1.3,-0.5,-2.5,-1.5,-3.4l-2.3,-2.2c-1.3,-1.3,-2,-2.8,-2,-4.7Z" class="g5_26" />
<path d="M133.7,210.6c0,0.1,-0.1,0.3,-0.3,0.3l-1.1,0c-0.1,0,-0.3,-0.2,-0.3,-0.3l0,-0.3c0,-0.2,0.2,-0.3,0.3,-0.3l1.1,0c0.2,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M133.7,209.2c0,0.2,-0.1,0.3,-0.3,0.3l-1.1,0c-0.1,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.1,0.2,-0.3,0.3,-0.3l1.1,0c0.2,0,0.3,0.2,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M133.7,211.8c0,0.2,-0.1,0.4,-0.3,0.4l-1.1,0c-0.1,0,-0.3,-0.2,-0.3,-0.4l0,-0.3c0,-0.1,0.2,-0.3,0.3,-0.3l1.1,0c0.2,0,0.3,0.2,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M136.1,214.1l0,103l19.6,0.4" class="g6_26" />
<path d="M136.1,854c-0.4,0,-0.7,0.1,-1,0.4c-0.2,0.3,-0.4,0.6,-0.4,1c0,0.4,0.2,0.7,0.4,1c0.3,0.3,0.6,0.4,1,0.4c0.4,0,0.8,-0.1,1.1,-0.4c0.2,-0.3,0.4,-0.6,0.4,-1c0,-0.4,-0.2,-0.7,-0.4,-1c-0.3,-0.3,-0.7,-0.4,-1.1,-0.4Z" class="g1_26" />
<path d="M142.8,841.2c0,-1.8,-0.7,-3.4,-2,-4.7c-1.3,-1.2,-3.2,-1.9,-5.6,-1.9l0,1.8c1.8,0,3.5,0.4,4.4,1.4c1,0.9,1.4,2.1,1.4,3.4c0,1.4,-0.4,2.5,-1.4,3.5l-2.4,2.2c-1.3,1.2,-1.9,2.8,-1.9,4.6c0,0.3,0.1,0.5,0.2,0.6c0.2,0.2,0.4,0.3,0.6,0.3c0.3,0,0.5,-0.1,0.7,-0.3c0.1,-0.1,0.2,-0.3,0.2,-0.6c0,-1.3,0.5,-2.5,1.4,-3.4l2.4,-2.2c1.3,-1.3,2,-2.9,2,-4.7Z" class="g1_26" />
<path d="M126.9,849.7c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0,-0.2,0.1,-0.3l1.6,-1.7c0.1,-0.1,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0,0.3,0.1c0.1,0.1,0.2,0.2,0.2,0.3c0,0.1,-0.1,0.2,-0.2,0.3l-1.6,1.7l-0.2,-0.2l0.2,0.2c-0.1,0.1,-0.2,0.1,-0.3,0.1c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm0.8,-0.9l0,0l-0.8,0.9l0.8,-0.9Z" class="g5_26" />
<path d="M124.5,847.1c-0.1,-0.1,-0.1,-0.2,-0.1,-0.2c0,-0.2,0.1,-0.3,0.2,-0.4l1.9,-1.4c0,0,0.1,-0.1,0.2,-0.1c0.2,0,0.3,0.1,0.4,0.2c0,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.2,-0.2,0.3l-1.9,1.4L125,847l0.1,0.2c-0.1,0.1,-0.2,0.1,-0.2,0.1c-0.2,0,-0.3,-0.1,-0.4,-0.2Zm0.4,-0.2l0,0Zm0,0l0,0Zm1.8,-1.4l0,0Z" class="g5_26" />
<path d="M122.7,843.3l0,-0.1c0,-0.2,0.1,-0.3,0.3,-0.4l2.3,-0.5c0.1,0,0.1,0,0.1,0c0.2,0,0.4,0.2,0.4,0.4c0.1,0,0.1,0,0.1,0.1c0,0.2,-0.2,0.3,-0.4,0.4l-2.3,0.5l0,-0.3l0,0.3l-0.1,0c-0.2,0,-0.3,-0.2,-0.4,-0.4Zm0.4,-0.1l0,0Zm2.3,-0.4l0,-0.1l0,0.1Z" class="g5_26" />
<path d="M125.4,840.1l-2.3,-0.4l0,-0.2l0,0.2c-0.2,0,-0.4,-0.2,-0.4,-0.4l0,-0.1c0.1,-0.2,0.2,-0.3,0.5,-0.3l2.3,0.3c0.2,0.1,0.4,0.3,0.4,0.5c0,0.3,-0.2,0.4,-0.4,0.4c-0.1,0,-0.1,0,-0.1,0Zm0.1,-0.4l0,0Zm-2.4,0l0,0Z" class="g5_26" />
<path d="M126.2,837.3l-2.1,-1.2l0.1,-0.2l-0.1,0.2c-0.1,-0.1,-0.2,-0.2,-0.2,-0.3c0,-0.1,0,-0.2,0.1,-0.3c0.1,-0.1,0.2,-0.2,0.3,-0.2c0.1,0,0.2,0,0.3,0.1l2,1.1c0.1,0.1,0.2,0.3,0.2,0.4c0,0.1,0,0.2,-0.1,0.2c0,0.2,-0.2,0.3,-0.3,0.3c-0.1,0,-0.2,-0.1,-0.2,-0.1Zm0.2,-0.4l0,0Zm-1.5,-0.8l0,0l-0.6,-0.3l0.6,0.3Z" class="g5_26" />
<path d="M128.3,835.2c-0.2,0,-0.3,-0.1,-0.4,-0.2l-1.3,-2l0.2,-0.1l-0.2,0.1c0,0,-0.1,-0.1,-0.1,-0.2c0,-0.1,0.1,-0.3,0.2,-0.4c0.1,0,0.2,0,0.3,0c0.1,0,0.2,0,0.3,0.1l1.3,2c0.1,0,0.1,0.1,0.1,0.2c0,0.2,0,0.3,-0.2,0.4c0,0,-0.1,0.1,-0.2,0.1Zm0,-0.5l0,0L127,832.8l1.3,1.9ZM127,832.8l0,0Z" class="g5_26" />
<path d="M130.7,833.7l-0.5,-2.3c0,0,0,-0.1,0,-0.1c0,-0.2,0.1,-0.4,0.3,-0.4c0.1,0,0.1,0,0.1,0c0.2,0,0.4,0.1,0.4,0.3l0.5,2.3l0,0.1c0,0.2,-0.1,0.4,-0.3,0.4c0,0,-0.1,0,-0.1,0c-0.2,0,-0.4,-0.1,-0.4,-0.3Zm0.4,-0.1l0,0Zm0,0l0,0Zm-0.7,-2.3l0,0l0.2,0l-0.2,0Z" class="g5_26" />
<path d="M126.6,841.2c0,-1.8,0.7,-3.4,1.9,-4.7c1.3,-1.2,3.2,-1.9,5.6,-1.9l0,1.8c-1.8,0,-3.4,0.4,-4.3,1.4c-1,0.9,-1.5,2.1,-1.5,3.4c0,1.4,0.5,2.5,1.5,3.5l2.3,2.2c1.3,1.2,2,2.8,2,4.6c0,0.3,-0.1,0.5,-0.3,0.6c-0.1,0.2,-0.3,0.3,-0.6,0.3c-0.2,0,-0.4,-0.1,-0.6,-0.3c-0.2,-0.1,-0.3,-0.3,-0.3,-0.6c0,-1.3,-0.4,-2.5,-1.4,-3.4l-2.4,-2.2c-1.2,-1.3,-1.9,-2.9,-1.9,-4.7Z" class="g5_26" />
<path d="M133.9,855.2c0,0.2,-0.1,0.4,-0.3,0.4l-1.1,0c-0.1,0,-0.3,-0.2,-0.3,-0.4l0,-0.2c0,-0.2,0.2,-0.4,0.3,-0.4l1.1,0c0.2,0,0.3,0.2,0.3,0.4l0,0.2Z" class="g5_26" />
<path d="M133.9,853.9c0,0.2,-0.1,0.3,-0.3,0.3l-1.1,0c-0.1,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.1,0.2,-0.3,0.3,-0.3l1.1,0c0.2,0,0.3,0.2,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M133.9,856.5c0,0.2,-0.1,0.3,-0.3,0.3l-1.1,0c-0.1,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.1,0.2,-0.3,0.3,-0.3l1.1,0c0.2,0,0.3,0.2,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M136.2,858.8l0,87.7l19.7,0.2" class="g6_26" />
<path d="M541.6,495.6c-0.4,0,-0.7,0.2,-1,0.4c-0.3,0.3,-0.4,0.6,-0.4,1c0,0.5,0.1,0.8,0.4,1.1c0.3,0.3,0.6,0.4,1,0.4c0.4,0,0.8,-0.1,1,-0.4c0.3,-0.3,0.5,-0.6,0.5,-1.1c0,-0.4,-0.2,-0.7,-0.5,-1c-0.2,-0.2,-0.6,-0.4,-1,-0.4Z" class="g1_26" />
<path d="M548.2,482.9c0,-1.9,-0.6,-3.4,-1.9,-4.7c-1.3,-1.3,-3.2,-2,-5.6,-2l0,1.8c1.8,0,3.4,0.5,4.4,1.4c0.9,1,1.4,2.1,1.4,3.5c0,1.3,-0.5,2.5,-1.4,3.4l-2.4,2.2c-1.3,1.3,-1.9,2.8,-1.9,4.7c0,0.2,0,0.4,0.2,0.6c0.2,0.1,0.4,0.2,0.6,0.2c0.3,0,0.5,-0.1,0.6,-0.2c0.2,-0.2,0.3,-0.4,0.3,-0.6c0,-1.4,0.5,-2.5,1.4,-3.5l2.4,-2.2c1.3,-1.3,1.9,-2.8,1.9,-4.6Z" class="g1_26" />
<path d="M532.4,491.3c-0.1,-0.1,-0.2,-0.2,-0.2,-0.3c0,-0.1,0.1,-0.2,0.2,-0.3L534,489c0.1,0,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0.1,0.3,0.1c0.1,0.1,0.1,0.2,0.1,0.3c0,0.2,0,0.3,-0.1,0.4l-1.6,1.6l-0.2,-0.1l0.2,0.1c-0.1,0.1,-0.2,0.2,-0.3,0.2c-0.1,0,-0.3,-0.1,-0.3,-0.2Zm0.3,-0.3l0,0Zm0.8,-0.8l0,0l-0.8,0.8l0.8,-0.8Z" class="g5_26" />
<path d="M530,488.8c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0.1,-0.3,0.2,-0.3l1.8,-1.4c0.1,-0.1,0.2,-0.1,0.3,-0.1c0.1,0,0.3,0,0.3,0.2c0.1,0,0.1,0.1,0.1,0.2c0,0.1,0,0.3,-0.1,0.4l-1.9,1.4l-0.1,-0.2l0.1,0.2c-0.1,0,-0.2,0,-0.3,0c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm0,0l0,0Zm1.9,-1.4l0,0Z" class="g5_26" />
<path d="M528.2,485c0,-0.1,0,-0.1,0,-0.1c0,-0.2,0.1,-0.4,0.3,-0.4l2.3,-0.5c0,0,0.1,0,0.1,0c0.2,0,0.4,0.1,0.4,0.3c0,0,0,0.1,0,0.1c0,0.2,-0.1,0.4,-0.3,0.4l-2.3,0.5l-0.1,-0.2l0.1,0.2c0,0,-0.1,0,-0.1,0c-0.2,0,-0.4,-0.1,-0.4,-0.3Zm0.4,-0.1l0,0Zm2.3,-0.5l0,0Z" class="g5_26" />
<path d="M530.9,481.8l-2.3,-0.4l0,-0.3l0,0.3c-0.3,-0.1,-0.4,-0.2,-0.4,-0.4c0,-0.1,0,-0.1,0,-0.1c0,-0.2,0.2,-0.4,0.4,-0.4c0.1,0,0.1,0,0.1,0l2.3,0.4c0.2,0,0.4,0.2,0.4,0.4c0,0,0,0.1,0,0.1c-0.1,0.2,-0.3,0.4,-0.5,0.4Zm0,-0.5l0,0Zm-2.3,0.1l0,0Z" class="g5_26" />
<path d="M531.6,478.9l-2,-1.1l0.1,-0.2l-0.1,0.2c-0.1,-0.1,-0.2,-0.3,-0.2,-0.4c0,-0.1,0,-0.1,0,-0.2c0.1,-0.2,0.3,-0.2,0.4,-0.2c0.1,0,0.2,0,0.2,0l2.1,1.2c0.1,0.1,0.2,0.2,0.2,0.4c0,0,0,0.1,-0.1,0.2c-0.1,0.1,-0.2,0.2,-0.4,0.2c0,0,-0.1,0,-0.2,-0.1Zm0.2,-0.3l0,0Zm-1.4,-0.9l0,0l-0.6,-0.3l0.6,0.3Z" class="g5_26" />
<path d="M533.8,476.8c-0.2,0,-0.3,-0.1,-0.4,-0.2l-1.3,-1.9l0.2,-0.1l-0.2,0.1C532,474.6,532,474.5,532,474.4c0,-0.1,0.1,-0.2,0.2,-0.3c0.1,-0.1,0.1,-0.1,0.2,-0.1c0.2,0,0.3,0.1,0.4,0.2l1.3,1.9c0.1,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.2,-0.2,0.3c-0.1,0.1,-0.2,0.1,-0.2,0.1Zm0,-0.4l0,0l-1.4,-2l1.4,2Zm-1.4,-2l0,0Z" class="g5_26" />
<path d="M536.2,475.3L535.7,473l0,-0.1c0,-0.2,0.1,-0.3,0.3,-0.4c0,0,0.1,0,0.1,0c0.2,0,0.4,0.1,0.4,0.3l0.5,2.3c0,0.1,0,0.1,0,0.1c0,0.2,-0.1,0.4,-0.3,0.5c-0.1,0,-0.1,0,-0.1,0c-0.2,0,-0.4,-0.2,-0.4,-0.4Zm0.4,-0.1l0,0Zm0,0l0,0ZM535.9,473l0,0l0.2,-0.1l-0.2,0.1Z" class="g5_26" />
<path d="M532.1,482.9c0,-1.9,0.6,-3.4,1.9,-4.7c1.3,-1.3,3.2,-2,5.6,-2l0,1.8c-1.8,0,-3.4,0.5,-4.4,1.4c-0.9,1,-1.4,2.1,-1.4,3.5c0,1.3,0.5,2.5,1.4,3.4l2.4,2.2c1.3,1.3,1.9,2.8,1.9,4.7c0,0.2,0,0.4,-0.2,0.6c-0.2,0.1,-0.4,0.2,-0.6,0.2c-0.3,0,-0.5,-0.1,-0.6,-0.2c-0.2,-0.2,-0.3,-0.4,-0.3,-0.6c0,-1.4,-0.5,-2.5,-1.4,-3.5L534,487.5c-1.3,-1.3,-1.9,-2.8,-1.9,-4.6Z" class="g5_26" />
<path d="M539.4,496.9c0,0.2,-0.2,0.3,-0.3,0.3l-1.1,0c-0.2,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1.1,0c0.1,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M539.4,495.6c0,0.1,-0.2,0.3,-0.3,0.3l-1.1,0c-0.2,0,-0.3,-0.2,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1.1,0c0.1,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M539.4,498.2c0,0.1,-0.2,0.3,-0.3,0.3l-1.1,0c-0.2,0,-0.3,-0.2,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1.1,0c0.1,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M541.7,500.5l0,53.6l19.7,0.1" class="g6_26" />
<path d="M544.9,1079.4c-0.4,0,-0.8,0.1,-1,0.4c-0.3,0.2,-0.4,0.6,-0.4,1c0,0.4,0.1,0.7,0.4,1c0.2,0.3,0.6,0.4,1,0.4c0.4,0,0.7,-0.1,1,-0.4c0.3,-0.3,0.4,-0.6,0.4,-1c0,-0.4,-0.1,-0.8,-0.4,-1c-0.3,-0.3,-0.6,-0.4,-1,-0.4Z" class="g1_26" />
<path d="M551.5,1066.6c0,-1.8,-0.6,-3.4,-1.9,-4.7c-1.3,-1.3,-3.2,-2,-5.6,-1.9l0,1.8c1.8,0,3.4,0.4,4.3,1.3c1,1,1.5,2.1,1.5,3.5c0,1.4,-0.5,2.5,-1.5,3.5l-2.3,2.2l0,-0.1c-1.3,1.3,-2,2.9,-2,4.7c0,0.2,0.1,0.4,0.3,0.6c0.1,0.2,0.4,0.3,0.6,0.3c0.2,0,0.4,-0.1,0.6,-0.3c0.2,-0.2,0.3,-0.4,0.3,-0.6c0,-1.3,0.4,-2.5,1.4,-3.5l2.4,-2.2l0,0.1c1.3,-1.3,1.9,-2.9,1.9,-4.7Z" class="g1_26" />
<path d="M535.6,1075.1c0,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.2,0,-0.3,0.1,-0.3l1.7,-1.7c0,-0.1,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0,0.3,0.1c0.1,0.1,0.1,0.2,0.1,0.3c0,0.1,0,0.2,-0.1,0.3l-1.6,1.7l-0.2,-0.2l0.2,0.2c-0.1,0,-0.2,0.1,-0.4,0.1c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm0.9,-0.9l0,0l-0.9,0.9l0.9,-0.9Z" class="g5_26" />
<path d="M533.3,1072.5c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0,-0.2,0.1,-0.3l1.9,-1.4c0.1,-0.1,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0.1,0.3,0.2c0.1,0.1,0.1,0.2,0.1,0.2c0,0.2,0,0.3,-0.2,0.4l-1.8,1.4l-0.2,-0.2l0.2,0.2c-0.1,0,-0.2,0.1,-0.3,0.1c-0.1,0,-0.3,-0.1,-0.3,-0.2Zm0.3,-0.3l0,0Zm0,0l0,0Zm1.9,-1.4l0,0Z" class="g5_26" />
<path d="M531.5,1068.7c0,0,0,-0.1,0,-0.1c0,-0.2,0.1,-0.4,0.3,-0.4l2.3,-0.5l0.1,0c0.2,0,0.4,0.1,0.4,0.4c0,0.2,-0.1,0.4,-0.3,0.5L532,1069l-0.1,-0.2l0.1,0.2c-0.1,0.1,-0.1,0.1,-0.1,0.1c-0.2,0,-0.4,-0.2,-0.4,-0.4Zm0.4,-0.1l0,0Zm2.3,-0.5l0,0Z" class="g5_26" />
<path d="M534.1,1065.5l-2.3,-0.4l0.1,-0.2l-0.1,0.2c-0.2,0,-0.3,-0.2,-0.3,-0.4c0,0,0,-0.1,0,-0.1c0,-0.2,0.2,-0.3,0.4,-0.3c0,0,0.1,0,0.1,0l2.3,0.3c0.2,0.1,0.3,0.3,0.3,0.5c0,0.2,-0.2,0.4,-0.4,0.4l-0.1,0Zm0.1,-0.4l0,0Zm-2.4,0l0,0Z" class="g5_26" />
<path d="M534.9,1062.7l-2,-1.2l0.1,-0.2l-0.1,0.2c-0.2,-0.1,-0.2,-0.2,-0.2,-0.4c0,0,0,-0.1,0,-0.2c0.1,-0.1,0.2,-0.2,0.4,-0.2c0.1,0,0.1,0,0.2,0.1l2,1.1c0.2,0.1,0.2,0.3,0.2,0.4c0,0.1,0,0.2,0,0.2c-0.1,0.2,-0.2,0.2,-0.4,0.2c-0.1,0,-0.1,0,-0.2,0Zm0.2,-0.4l0,0Zm-1.4,-0.8l0,0l-0.6,-0.4l0.6,0.4Z" class="g5_26" />
<path d="M537,1060.5c-0.1,0,-0.2,0,-0.3,-0.1l-1.3,-2l0.1,-0.1l-0.1,0.1c-0.1,-0.1,-0.1,-0.1,-0.1,-0.2c0,-0.2,0,-0.3,0.2,-0.4c0,0,0.1,-0.1,0.2,-0.1c0.1,0,0.3,0.1,0.4,0.2l1.3,2c0,0,0.1,0.1,0.1,0.2c0,0.1,-0.1,0.3,-0.2,0.4c-0.1,0,-0.2,0,-0.3,0Zm0,-0.4l0,0l-1.3,-1.9l1.3,1.9Zm-1.3,-1.9l0,0Z" class="g5_26" />
<path d="M539.4,1059.1l-0.5,-2.3c0,-0.1,0,-0.1,0,-0.1c0,-0.2,0.2,-0.4,0.4,-0.4c0,-0.1,0,-0.1,0.1,-0.1c0.2,0,0.3,0.2,0.4,0.4l0.5,2.3l0,0.1c0,0.2,-0.1,0.3,-0.4,0.4c-0.2,0,-0.4,-0.1,-0.5,-0.3Zm0.5,-0.1l0,0Zm0,0l0,0Zm-0.7,-2.3l0,0l0.2,0l-0.2,0Z" class="g5_26" />
<path d="M535.4,1066.6c0,-1.8,0.6,-3.4,1.9,-4.7c1.3,-1.3,3.2,-2,5.6,-1.9l0,1.8c-1.8,0,-3.5,0.4,-4.4,1.3c-1,1,-1.4,2.1,-1.4,3.5c0,1.4,0.4,2.5,1.4,3.5l2.4,2.2l0,-0.1c1.3,1.3,1.9,2.9,1.9,4.7c0,0.2,-0.1,0.4,-0.2,0.6c-0.2,0.2,-0.4,0.3,-0.6,0.3c-0.3,0,-0.5,-0.1,-0.7,-0.3c-0.1,-0.2,-0.2,-0.4,-0.2,-0.6c0,-1.3,-0.5,-2.5,-1.4,-3.5l-2.4,-2.2l0,0.1c-1.3,-1.3,-1.9,-2.9,-1.9,-4.7Z" class="g5_26" />
<path d="M542.6,1080.6c0,0.2,-0.1,0.3,-0.3,0.3l-1,0c-0.2,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.1,0.1,-0.3,0.3,-0.3l1,0c0.2,0,0.3,0.2,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M542.6,1079.3c0,0.2,-0.1,0.3,-0.3,0.3l-1,0c-0.2,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1,0c0.2,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M542.6,1081.9c0,0.2,-0.1,0.3,-0.3,0.3l-1,0c-0.2,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1,0c0.2,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g5_26" />
<path d="M545,1084.2l0,51.2l19.7,0.2" class="g6_26" />
</svg>