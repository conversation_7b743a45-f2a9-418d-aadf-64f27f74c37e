@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}
body {
	font-size:120%;
	line-height:150%;
	text-align:justify;
}

.chapterHeading {
	font-size:140%;
	font-weight:bold;
	color:#03C;
	
}
img{
max-width:100%;
}
.chapterNumber {
	font-size:36px;
	font-weight:bold;
}

.subHeading {
	font-size:120%;
	color:#06C;
	margin-bottom:1%;
	font-weight:bold;
}
.meaning {
	font-size:95%;
}
.author {
	text-align:right;
	color: #00AEEF;
}

.image {
text-align:center;
}

.activity {
	background: #f8c1d9;
	padding:2%;
}
.activity2 {
rgb(0,166,80)
	background:	#cce7d3;
		padding:2%;
}
.activity3 {
	background: #C9F;
		padding:2%;
}
.englishMeaningblue{
	font-size:0.9em;
	font-family:Arial, Helvetica, sans-serif;
	color: #fff;
}
.englishMeaningred{
	font-size:0.9em;
	font-family:Arial, Helvetica, sans-serif;
	color: #FF0000;
font-weight:normal;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
.activityBox{
background-color:#cce7d3;
padding: 15px;
font-size:0.9em;
line-height:150%;
border:2px solid rgb(0,166,80);
}
.activityBox1{
background-color:#F8C1D9;
padding: 15px;
line-height:150%;
border-top:2px solid rgb(147,149,152);
border-bottom:2px solid rgb(147,149,152);
}
.activityBox2{
background-color:#cce7d3;
padding: 15px;
line-height:150%;
border:2px solid rgb(0,166,80);
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:rgb(47,49,146);
padding:10px;
}
/* Chapter number */
h4
{
color:#FF0000;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#00AEEF;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#00AEEF;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* if Mathematics or science book use */
#MathSc img
{
	position:relative;
	top:15px;
}
#MathSc .img1
{
	position:relative;
	top:10px;
}
#MathSc .img2
{
	position:relative;
	top:10px;
}
#MathSc .img3
{
	position:relative;
	top:10px;
}
#MathSc .img4
{
	position:relative;
	top:10px;
}
#MathSc .img5
{
	position:relative;
	top:10px;
}
#MathSc .img6
{
	position:relative;
	top:9px;
}
#MathSc .img7
{
	position:relative;
	top:10px;
}
#MathSc .img8
{
	position:relative;
	top:20px;
}
#MathSc .img9
{
	position:relative;
	top:10px;
}
#MathSc .img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:95%;
}
.clear
{
	clear:both;
}

.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
.blue{
	color: #00AEEF;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	font-weight:normal;
font-size:0.9em;
}
.englishMeaningcolor{
	font-family:Arial, Helvetica, sans-serif;
	color: #00AEEF;
font-size:0.8em;
}
.bold
{
	font-size:100%;

}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}.chapterNumber {
	font-size: 120%;
	color: #F00;
	text-align:right;
}
.subHeading1 {
	font-size:120%;
	color: #C3C;
	margin-bottom:1%;
	font-weight:bold;
}
#A {
	color: #09F;
}
#a {
	color: #09F;
}
p .bold {
	color: #000;
}
.chapterNumber p {
	color: #000;
	text-align: left;
	font-size: 120%;
	font-family: Walkman-Chanakya-905;
}
#a {
	color: #06C;
}
.bold {
	color: #000;
}
.bold {
	color: #000;
}
.bold {
	color: #000;
}
#a {
	font-weight: normal;
}
B {
	font-size: 120%;
}
.superscript{
position:relative;
top:-10%;
font-size: 80%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
vertical-align:baseline;
bottom:-40%;
font-size: 80%;
font-family:Arial, Helvetica, sans-serif;
}
.author2 {
	text-align:right;
}

.line{
border-bottom:5px solid #00AEEF;
}

div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 95%;
position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:40%;
line-height:150%;
margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}


.conc

{

color:#006699;

}
.img_wid

{

margin-left: auto;

margin-right: auto;

display: block;

width:80%;

}
table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}
#prelims .para-style-override-18, .char-style-override-29, .char-style-override-18
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3, .para-style-override-14
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
 @media only screen and (max-width: 767px) {


div.chapter_pos


{

top:20%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;

}

.cover_img_small

{

width:90%;

}

}