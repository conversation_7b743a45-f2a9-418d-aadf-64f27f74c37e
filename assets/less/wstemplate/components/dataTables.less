@media
only screen
and (max-width: 760px), (min-device-width: 768px)
and (max-device-width: 1024px)  {


  /* Hide table headers (but not display: none;, for accessibility) */
  #viewuser thead tr,#viewuserstd thead tr,#viewAccesscode thead tr,#viewBook thead tr , #pubTable thead tr{
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  #batchUsers .batchusers tbody tr:first-child , #batchUsers .managebooks tbody tr:first-child {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }


  #viewuser tr:nth-child(odd) , #viewuserstd tr:nth-child(odd),#viewAccesscode tr:nth-child(odd),#viewBook tr:nth-child(odd) , #pubTable tr:nth-child(odd) ,  #batchUsers .batchusers tr:nth-child(odd) , #batchUsers .managebooks tr:nth-child(odd){
    background: #ccc;
  }

  #viewuser td , #viewuserstd td ,#viewAccesscode td ,#viewBook td,  #pubTable td ,  #batchUsers .batchusers td , #batchUsers .managebooks td{
    /* Behave  like a "row" */
    border: none;
    border-bottom: 1px solid #eee;
    position: relative;

  }

  #viewuser td:before , #viewuserstd td:before ,#viewAccesscode td:before , #viewBook td:before ,  #pubTable td:before ,  #batchUsers .batchusers td:before , #batchUsers .managebooks td:before {
    /* Now like a table header */
    position: absolute;
    /* Top/left values mimic padding */
    top: 25%;
    left: 6px;
    width: 100%;
    padding-right: 10px;
    white-space: nowrap;
  }


    /*
    Label the data for managebooks
  You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
    */
  #batchUsers .managebooks td:nth-of-type(1):before { content: "Name"; }
  #batchUsers .managebooks td:nth-of-type(2):before { content: "Username"; }
  #batchUsers .managebooks td:nth-of-type(3):before { content: "Email"; }
  #batchUsers .managebooks td:nth-of-type(4):before { content: "Mobile"; }
  /*
  /*
   Label the data for batchUsers
 You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
   */
  #batchUsers .batchusers td:nth-of-type(1):before { content: "Book Id"; }
  #batchUsers .batchusers td:nth-of-type(2):before { content: "Username"; }
  #batchUsers .batchusers td:nth-of-type(3):before { content: "Expiry Date"; }
  #batchUsers .batchusers td:nth-of-type(4):before { content: "Date Added"; }
  /*
  /*
  Label the data for viewuserstd
You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
  */
  #viewuserstd td:nth-of-type(1):before { content: "Name"; }
  #viewuserstd td:nth-of-type(2):before { content: "Email"; }
  #viewuserstd td:nth-of-type(3):before { content: "Mobile"; }
  #viewuserstd td:nth-of-type(4):before { content: "Admission No."; }
  #viewuserstd td:nth-of-type(5):before { content: "Delete"; }
  /*
   Label the data for viewuser
You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
  */
  #viewuser td:nth-of-type(1):before { content: "Name"; }
  #viewuser td:nth-of-type(2):before { content: "Email"; }
  #viewuser td:nth-of-type(3):before { content: "Mobile"; }
  #viewuser td:nth-of-type(4):before { content: "Delete"; }
  /*
  Label the data for viewAcesscode
You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
  */
  #viewAccesscode td:nth-of-type(1):before { content: "Serial No."; }
  #viewAccesscode td:nth-of-type(2):before { content: "Access Code"; }
  #viewAccesscode td:nth-of-type(3):before { content: "Status"; }
  #viewAccesscode td:nth-of-type(4):before { content: "Username"; }
  #viewAccesscode td:nth-of-type(5):before { content: "Date Created"; }
  #viewAccesscode td:nth-of-type(6):before { content: "Date Redeemed"; }

  /*
      Label the data for viewAcesscode
  You could also use a data-* attribute and content for this. That way "bloats" the HTML, this way means you need to keep HTML and CSS in sync. Lea Verou has a clever way to handle with text-shadow.
      */
  #viewBook td:nth-of-type(1):before { content: "Book Id"; }
  #viewBook td:nth-of-type(2):before { content: "Title"; }
  #viewBook td:nth-of-type(3):before { content: "Isbn"; }
  #viewBook td:nth-of-type(4):before { content: "Publisher"; }
  #viewBook td:nth-of-type(5):before { content: "Status"; }
  #viewBook td:nth-of-type(6):before { content: "Number of copies"; }
  #viewBook td:nth-of-type(7):before { content: "Validity (in days)"; }


  button#toggle {
    position:absolute;
    left:0;
    border-radius:50%;
    background:#F79420;
    border: 1px solid white;
  }
  #viewuser tbody ,  #viewuserstd tbody ,#viewAccesscode tbody , #viewBook tbody ,  #pubTable tbody , #batchUsers .batchusers tbody , #batchUsers .managebooks  tbody
  {
    display:block !important;
  }
  #viewuser td:before,#viewuserstd td:before,#viewAccesscode td:before , #viewBook td:before ,   #pubTable td:before , #batchUsers .batchusers td:before , #batchUsers .managebooks td:before{
    text-align:left;
  }
  #viewuser td,#viewuserstd td, #viewAccesscode td ,#viewBook td ,  #pubTable td , #batchUsers .batchusers td , #batchUsers .managebooks td  {
    text-align:right !important;
    height:30px;
    padding-left: 40%;
  }
  #batchUsers .batchusers td , #batchUsers .managebooks td {
    height: 40px !important;
  }

  #viewuser td:before,#viewuserstd td:before,#viewAccesscode td:before , #viewBook td:before ,   #pubTable td:before , #batchUsers .batchusers td:before , #batchUsers .managebooks td:before
  {
    padding-left:6%;

  }

  #viewuser button.btn.btn-primary.mx-2 ,#viewuserstd button.btn.btn-primary.mx-2 , #viewAccesscode button.btn.btn-primary.mx-2,#viewBook button.btn.btn-primary.mx-2 , #batchUsers .batchusers button.btn.btn-primary.mx-2, #batchUsers .managebooks button.btn.btn-primary.mx-2{
    margin-left:1% !important;
  }
   #viewuserstd .admissionbox
  {
    width:35%;
  }
  #viewuser input#admissionNoBox0,#viewAccesscode input#admissionNoBox0 ,#viewBook input#admissionNoBox0 ,#batchUsers .batchusers input#admissionNoBox0, #batchUsers .managebooks input#admissionNoBox0{
  margin-right:0px !important;
    width:35%;

  }
  #viewuser tbody td,#viewuserstd tbody td,#viewAccesscode tbody td , #viewBook tbody td ,  #pubTable tbody td , #batchUsers .batchusers tbody td, #batchUsers .managebooks tbody td
  {
    display:none;
  }
  #viewuser tbody td:first-child, #viewuserstd tbody td:first-child, #viewAccesscode tbody td:first-child, #viewBook tbody td:first-child ,  #pubTable tbody td:first-child , #batchUsers .batchusers tbody td:first-child, #batchUsers .managebooks tbody td:first-child
  {
    display:block;
  }
}

@media (min-width:576px)
{
  button#toggle
  {
    display:none !important;
  }

}

@media (max-width:575px)
{
  ul.pagination .page-link{
    padding:5px !important;
  }
}
