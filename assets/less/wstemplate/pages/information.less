@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Information Page Styles
.information {
  #contents {
    .info-description {
      //max-height: 85px;
      overflow: hidden;
      //text-overflow: ellipsis;
      //display: -webkit-box;
      //-webkit-line-clamp: 3;
      //-webkit-box-orient: vertical;
      margin-bottom: 7px;
      &.with-answer-desc, &.humour-desc {
        overflow: unset;
        margin-bottom: 0;
        text-overflow: unset;
        display: initial;
        -webkit-line-clamp: unset;
        -webkit-box-orient: unset;
      }
      h1, h2, h3, h4, h5, h6 {
        color: @dark-gray;
        font-size: 1.25rem;
        margin-bottom: .5rem;
      }
      img {
        max-width: 100%;
        @media @extraSmallDevices, @smallDevices {
          width: 100%;
          height: auto;
        }
      }
      p {
        margin-bottom: 10px;
      }
      table {
        max-width: 100%;
      }
    }
    .created-date {
      font-size: 12px;
      color: @light-gray;
      font-style: italic;
      strong {
        font-weight: normal;
      }
    }
    .info-title {
      color: @black;
      &:hover {
        color: @theme-primary-color;
        text-decoration: underline;
      }
    }
    p, li {
      color: @dark-gray;
    }
    ol, ul {
      padding-left: 15px;
    }
    blockquote {
      margin-left: 10px;
    }
    .share-info {
      width: 30px;
      height: 30px;
      cursor: pointer;
      padding: 15px;
      i {
        font-size: 17px;
      }
    }
    .info-list {
      position: relative;
      .divider {
        position: absolute;
        bottom: -2px;
        left: 0;
        right: 0;
        background-image: url("../../images/ws/horizontal-separator-line.svg");
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        width: 100%;
        height: 4px;
        opacity: 0.5;
        @media @extraSmallDevices, @smallDevices {
          background-size: cover;
        }
      }
      &:last-child {
        .divider {
          display: none;
        }
      }
    }
  }
  .show-info-details {
    font-size: 14px;
    &:hover {
      text-decoration: underline;
    }
  }
}

// Information Detail Page Styles
.information-detail {
  .prev-info-link, .next-info-link {
    a {
      &:hover {
        span {
          text-decoration: underline;
        }
      }
    }
  }
  #contents {
    .info-description {
      overflow: unset;
      margin-bottom: 0;
      text-overflow: unset;
      display: initial;
      -webkit-line-clamp: unset;
      -webkit-box-orient: unset;
    }
  }
}