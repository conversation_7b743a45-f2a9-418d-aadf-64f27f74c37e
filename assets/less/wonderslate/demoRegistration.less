@import "color.less";
@import "common.less";

// Desktops
@extraLargeScreen: ~"only screen and (min-width: 1201px) and (max-width: 1300px)";
// laptops
@largeScreen: ~"only screen and (min-width: 992px) and (max-width: 1200px)";
//Tabs
@mediumScreen: ~"only screen and (min-width: 768px) and (max-width: 991px)";
// Mobiles
@smallScreen: ~"only screen and (max-width: 767px)";

body {
  @media @smallScreen {
    background: rgb(229,158,250,0.5);
    background: linear-gradient(0deg, rgba(173,34,222,0.8) 0%, rgba(229,158,250,0.7) 30%, rgba(255,255,255,1) 100%);
  }
}
.digital-library-page {
  //background-color: #f9f9f9;
  //Header
  header {
    position: relative;
    width: 90%;
    margin: 10px auto !important;
    background: transparent;
    @media (max-width: 1200px) {
      width: 100%;
      margin: 0 auto !important;
    }
    .digital-library-logo {
      display: flex;
      align-items: center;
      span.logo {
        padding: 0;
        display: inline-block;
        align-items: center;
        height: 60px;
        img {
          width: 180px;
          height: 100%;
          @media @smallScreen {
            width: 120px;
          }
        }
      }
      span.divider {
        img {
          height: 60px;
        }
      }
      span.tagline {
        color: #C7C7C7;
        font-size: 18px;
        letter-spacing: 1px;
        @media @smallScreen {
          font-size: 14px;
          color: #777;
        }
      }
    }
  }
  .curve-bg {
    position: absolute;
    top: -30px;
    right: 0;
    z-index: -1;
    @media @smallScreen {
      display: none;
    }
    img {
      width: 800px;
    }
  }
  //Content
  .banner_wrap {
    width: 90%;
    margin: 0 auto;
    @media (max-width: 1200px) {
      width: 100%;
    }
    .banner_info {
      h2 {
        color: #FF5700;
        text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
        line-height: 1.3;
        //-webkit-text-stroke: .5px white;
        @media @smallScreen {
          //color: #ffffff;
          font-size: 1.5rem;
          line-height: 1.5;
          text-align: center;
        }
        @media (max-width: 340px) {
          font-size: 1.35rem;
        }
      }
      p {
        color: #A8A8A8;
        br {
          display: none;
          @media @smallScreen {
            display: block;
          }
        }
        @media @smallScreen {
          color: #212121;
          line-height: 22px;
          text-align: center;
          font-size: 15px;
        }
        @media (max-width: 340px) {
          font-size: 13.4px;
        }
      }
      .icon-lists {
        .icon-list {
          text-align: center;
          @media @smallScreen {
            margin-bottom: 15px;
          }
          p {
            font-size: 14px;
            color: rgba(68, 68, 68, 0.48);
            @media @smallScreen {
              color: #212121;
            }
            br {
              display: block;
            }
          }
        }
      }
      .bg-white {
        @media @smallScreen {
          justify-content: center;
          background: transparent !important;
        }
        p {
          font-size: 14px;
          box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
        }
      }
    }
    .banner_register_form {
      margin-top: -80px;
      @media @smallScreen {
        margin-top: 0;
      }
      .form-info {
        background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        @media @smallScreen {
          background: radial-gradient(94.15% 263.19% at 5.85% 8.03%, rgba(255, 255, 255, 0.68) 0%, rgba(247, 247, 247, 0.68) 100%);
        }
        .form-horizontal {
          margin-top: -70px;
          @media @smallScreen {
            margin-top: -60px;
          }
          .form-group {
            background: radial-gradient(155.78% 433.79% at -20.98% -8.24%, #FFD000 0%, #FF5700 100%);
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            h3 {
              color: rgba(255, 255, 255, 0.48);
              font-size: 1.5rem;
              @media @smallScreen {
                font-size: 1.25rem;
              }
            }
            input {
              color: #212121;
              height: 35px;
              @media @smallScreen {
                font-size: 15px;
              }
              &:focus {
                box-shadow: none;
              }
            }
            .alert {
              @media (max-width: 340px) {
                font-size: 14px !important;
              }
            }
          }
        }
        button.submit-btn {
          background: #FF9901;
          border: 1px solid #FFFFFF;
          box-sizing: border-box;
          box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          width: 85%;
        }
        .bottom-image {
          margin-bottom: -70px;
          @media @smallScreen {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}
