@import "../wstemplate/theme/colors.less";
@import "../wstemplate/theme/fonts.less";
@import "../wstemplate/theme/responsive.less";

.chapter-wrapper {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
  position: fixed;
  background: none;
  border:none;
  width: 290px;
  height: calc(100vh - 190px);
  overflow-y: auto;
  //padding-top:2rem;
  display: flex;
  justify-content: center;

  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    position: static;
    width: 100%;
    height: 100%;
    background: @white;
    margin-top: 0 !important;
  }
  ol {
    padding: 0;
    margin: 0;
    @media @extraSmallDevices, @smallDevices, @mediumDevices{
      width: 100%;
      padding: 0 10px;
    }
  }

  li {
    width: 246px;
    min-height: 50px;
    box-shadow: 0 0 10px @gray-light-shadow;
    list-style-position: inside;
    list-style-type: none;
    margin-bottom: 1rem;
    border-radius: 5px;
    display: flex;
    align-items: center;
    &:last-child{
      margin-bottom: 8rem;
    }
    @media @extraSmallDevices, @smallDevices, @mediumDevices{
      width: 100%;
      &:last-child{
        margin-bottom:6rem;
      }
    }
    &::before {
      counter-increment: section;
      content: " " counter(section) ". ";
      margin-left: 10px;
      color: @theme-primary-color;
      font-size: 12px;
    }
    &.orangeText {
      background: @theme-primary-color;
      color: @white;
      font-weight: @medium;
      &::before {
        color: @white;
      }
      a {
        color: @white;
        font-weight: @medium;
      }
    }

    a {
      text-decoration: none;
      color: @theme-primary-color;
      font-size: 12px;
      display: flex;
      padding: 1px 5px;
      align-items: center;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
  &::-webkit-scrollbar {
    display: none;
  }

}

.notes {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  &:focus{
    outline:0;
  }

  background: @white;
  border:1px solid @theme-primary-color;
  box-sizing: border-box;
  box-shadow: 0 0 10px @gray-light-shadow;
  border-radius: 4px;
  color: @theme-primary-color;
  font-size: 10px;
  width: 117px;

  i {
    color: lighten(@dark-gray,25%);
    font-size: 16px;
    margin-right: 5px;
  }

  height: 36px;
}

.revise {

  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border:none;
  &:focus{
    outline:0;
  }

  background: @white;
  border: 1px solid lighten(@dark-gray,25%);
  box-sizing: border-box;
  box-shadow: 0 0 10px @gray-light-shadow;
  border-radius: 44px;
  color: lighten(@dark-gray,25%);
  font-size: 10px;

  i {
    color: lighten(@dark-gray,25%);
    font-size: 16px;
    margin-right: 5px;
  }

  height: 36px;
  width: 117px;
}
.read-book-chapters-wrapper{
  counter-reset: section;
  @media @extraSmallDevices, @smallDevices {
    padding: 10px;
  }
}
.all-container{
  .container-wrapper{
    width: 600px;
    margin-top: 1rem !important;
    @media @extraSmallDevices, @smallDevices{
      width: 100%;
      margin:0 auto;
      border:none;
      margin-top: 1rem;
      border-radius: 4px;
    }
    .media .addtodo-wrapper{
      button{
        width: 50px;
        height: 114px;
        color:@theme-primary-color;
        font-size: 14px;
        padding: 8px;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        background:rgba(239, 239, 239, 0.35);
        &.btn-success{
          background: @theme-primary-color;
          border:none;
          span{
            color: @white;
            &:last-child{
              color:@white;
            }
          }
          i{
            color:@white;
          }
        }
        span{
          color:@theme-primary-color;
          font-size: 20px;
          display: block;
          &:last-child{
            font-size:10px;
            white-space: normal;
            font-style: italic;
            margin-top: 0;
          }
        }
      }
    }
  }

  #allAddButton .dropdown{
    position: static;
  }
}
.all-menu{
  position: fixed;
  a{
    background:@theme-primary-color;
    box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 50px;
    width: 75px;
    height: 75px;
    color:@white;
    text-align: center;
    display: block;
    margin-top: 1rem;
    &:hover{
      text-decoration: none;
    }
    &:focus{
      text-decoration: none;
      //color:#000;
      background:@theme-primary-color;
    }
    i{
      font-size: 18px;
      margin-top: 20px;
    }
    span{
      font-size: 10px;
      display: block;
      text-align: center;
    }
  }
}
.read-content{
  padding-bottom: 4rem;
}
.all-container {
  .container-wrapper {
    div > .media {
      flex-direction: column;
      p{
        line-height: 1;
      }
      .title{
        padding: 0 10px;
        color: @dark-gray;
        font-size: 12px;
      }
      width: 100%;
      padding: 0;
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      .readnow{
        display: flex;
        align-items: center;
        padding: 0 1rem;
        width: 100%;
        &:hover{
          text-decoration: none;
        }
      }
      .box {
        display: flex;
        align-items: center;
        justify-content: center;

        p {
          font-size: 9px;
          margin: 0;
          text-align: center;
          top: 0;
          color: @white;
          font-weight: @bold;
        }

        i {
          width: 32px;
          height: 24px;
          margin: 0 auto;
        }

        width: 42px;
        height: 52px;
        border-radius: 5px;
        margin: 0.5rem 0;

        &.blue {
          background: radial-gradient(109.09% 109.09% at 0% 0%, #2D9CDB 0%, #2F80ED 100%);

          i {
            background: url("../../images/ws/pdf.svg") center center no-repeat;
          }
        }

        &.green {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #C7F24C 5.08%, rgb(85 115 0 / 0.8) 76.95%);

          i {
            background: url("../../images/ws/link.svg") center center no-repeat;

          }
        }

        &.yellow {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #F2C74C 0%, #F2994A 46.86%);

          i {
            background: url("../../images/ws/flashcard.svg") center center no-repeat;
          }
        }

        &.pink {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #F24CE1 0%, rgb(183 7 206 / 0.9) 76.95%);

          i {
            background: url("../../images/ws/video.svg") center center no-repeat;
          }
        }

        &.lightgreen {
          background: radial-gradient(100% 100% at 0% 0%, #49E859 0%, #007D0C 100%);

          i {
            background: url("../../images/ws/notes.svg") center center no-repeat;
          }
        }

        &.violet {
          background: radial-gradient(142.42% 142.42% at 0% 0%, #BB6BD9 0%, #7B24CD 99.48%);

          i {
            background: url("../../images/ws/mcq1.svg") center center no-repeat;
          }
        }
        &.darkgreen{
          background: radial-gradient(142.42% 142.42% at 0% 0%, #4CF2E8 5.08%, #006963 70.31%);;

          i {
            background: url("../../images/ws/mindmap.svg") center center no-repeat;
          }
        }
      }
    }
  }
}
.chapterList{
  @media @extraSmallDevices, @smallDevices{
    //position: absolute;
    //z-index: 98;
    //background: #ffffff;
    //height: 100vh;
    display: block;
  }
  display: flex;
  justify-content: center;
  .tab-content{
    width:100%;
  }
}
.generateTest{
  background:@theme-primary-color;
  box-sizing: border-box;
  box-shadow: 0 0 10px @gray-light-shadow;
  border-radius: 5px;
  //height: 32px;
  color:@white;
  font-size: 12px;
  display: flex;
  align-items: center;
  padding: 8px;
  i{
    color:@white;
    font-size: 16px;
  }
  @media @extraSmallDevices, @smallDevices{
    bottom:85px;
    position: fixed;
    width: 90%;
    z-index: 99;
    display: flex;
    justify-content: center;
  }
  &:hover{
    text-decoration: none;
    color:@white;
  }
}
.actionMenu{
  @media @extraSmallDevices, @smallDevices {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    background:@theme-primary-color;
    box-shadow: 0 0 4px @gray-dark-shadow;
    border-radius: 20px 20px 0 0;
  }
  .all-menu{
    @media @extraSmallDevices, @smallDevices{
      position: static;
      display: flex;
      width: 100%;
      justify-content: space-between;
      a{
        border-radius: 0;
        margin-top: 0;
        display: flex;
        align-items: center;
        background-image: url('../../images/ws/menu-line.png');
        background-position: right;
        background-repeat: no-repeat;
        padding-right: 10px;
        &:last-child{
          background: none;
        }
        span{
          font-size: 11px;
        }
        i{
          margin-top: 0;
          font-size: 18px;
        }
      }
    }
  }
}
.chapter-head{
  font-size: 20px;
  background: @theme-primary-color;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
#allAddButton{
  margin-top: 2rem;
  @media @extraSmallDevices, @smallDevices{
    margin-top: 0;
  }
}
.all-container .container-wrapper{
  min-height: auto;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    margin: 0 auto;
  }
  .media{
    margin-bottom: 0;
    padding: 0 1rem;
  }
}
select {
  color:@theme-primary-color !important;
  &.type {
    width: 200px;
    border: 1px solid @theme-primary-color;
  }
  &.sortby{
    width:100px;
    border: 1px solid @theme-primary-color;
  }
}
.modal{
  background: @loader-color !important;
  z-index: 9992;
}
.web-url,.video-url{
  .modal{
    .modal-header{
      display: none;
    }
    .modal-content{
      width: 100%;
    }
    .modal-body{
      form{
        margin-top: 0;
      }
      input{
        width: 100%;
        border-bottom: 1px solid lighten(@dark-gray,25%);
      }
    }
    .modal-footer{
      button{
        background: transparent;
        &.cancel{
          color: lighten(@dark-gray,25%);
          text-transform: capitalize;
        }
        &.saveLink{
          color:@theme-primary-color;
          font-size: 14px;
          font-weight: @bold;
        }
      }
    }
  }
}
#htmlContent{
  min-width: 768px;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    min-width: 100%;
  }
  .annotator-hlh,.annotator-hl,.annotator-hl-temporary{

  }
}
.read-back-btn{
  width: 100px;
  border-radius: 18px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  z-index: 1;
  top: 0;
  color: lighten(@dark-gray,25%);
  font-size: 14px !important;
  background: none;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    justify-content: flex-start;
  }
  &:focus{
    outline: none;
  }
  i{
    color: lighten(@dark-gray,25%);
    font-size: 34px;
    background: @theme-primary-color;;

  }
  &:hover{
    text-decoration: none;
  }
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    left: 0;
  }
}

.shine {
  background: @white;
  background-image: @white;
  background-repeat: no-repeat;
  background-size: 800px 104px;
  display: inline-block;
  position: relative;

  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: forwards;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-name: placeholderShimmer;
  -webkit-animation-timing-function: linear;
}

box {
  height: 104px;
  width: 100%;
}

div.line-wrapper {
  display: inline-flex;
  flex-direction: column;
  margin-left: 25px;
  margin-top: 15px;
  vertical-align: top;
}

lines {
  height: 10px;
  margin-top: 10px;
  width: 100%;
}

photo {
  display: block!important;
  width: 325px;
  height: 30px;
  margin-top: 15px;
}
.mt-20{
  margin-top: 10px;
}

@-webkit-keyframes placeholderShimmer {
  0% {
    background-position: -468px 0;
  }

  100% {
    background-position: 468px 0;
  }
}
.flexAlign{
  display: flex;
  justify-content: center;
}

.line-seperator{
  background: url('../../images/ws/chapter-line.png') center center no-repeat;
  background-size:contain;
  position: fixed;
  width: 1px;
  height: 500px;
  margin-top: 1rem;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    display: none;
  }
}
#htmlreadingcontent{
  margin: 1rem;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    //margin-top: 2rem !important;
  }
}
.pdfbutton{
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  padding: 1.2rem 4rem;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    padding: 0 20px;
  }
  button{
    background:@theme-primary-color;
    color:@white;
    border: none;
    //.myflex;
    display: flex;
    align-items: center;
    justify-content: center;

    height:32px;
    border-radius:4px;
    padding: 0 10px;
  }
}
.subMenu{
  &.addFix{
    position: sticky;
    top: 65px;
    background: @white;
    z-index: 99;
    padding-top: 10px;
    border-top:1px solid #ececec;
    -webkit-box-shadow: 0 8px 6px -6px @gray-light-shadow;
    -moz-box-shadow: 0 8px 6px -6px @gray-light-shadow;
    box-shadow: 0 8px 6px -6px @gray-light-shadow;
    padding-bottom: 10px;
    transition: all 0.5s;

  }
}
.bookTemplate{
  &.reFix{
    #book-read-material{
      margin-top: 2rem;
    }
    .read-content .export-notes{
      top:65px;
    }
  }
  .content-wrapper {
    .price-wrapper{
      padding: 0;
    }
  }
}
.select_box{
  width: 170px;
  overflow: hidden;
  border: none;
  position: relative;
  border-radius:5px;
  //padding: 10px 0;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    margin-right: 10px;
    width: 100%;
  }
}
.select_box:after{
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid @theme-primary-color;
  position: absolute;
  top: 16px;
  right: 5px;
  content: "";
  z-index: 999;
}
.select_box select{
  width: 183px;
  border: 0;
  position: relative;
  z-index: 99;
  background: none;
  font-style: italic;
  font-size: 12px;
  background: @dark-gray;
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    width: 99%;
  }
}
.sharethis-inline-share-buttons{
  position: sticky;
  bottom:0;
  width: 100%;
  background: @white;
  padding:10px;
  -webkit-box-shadow: 0 -4px 3px @gray-light-shadow;
  -moz-box-shadow: 0 -4px 3px @gray-light-shadow;
  box-shadow: 0 -4px 3px @gray-light-shadow;
  margin-left: 2rem;
  z-index: 1 !important;
}
.bookTemplate .read-content .export-notes{
  top:80px;
}
#main-wrapper{
  max-width: 100%;
  .subMenu{
    @media @extraSmallDevices, @smallDevices, @mediumDevices{
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 0 0 10px 10px;
      padding-bottom: 1.5rem;
    }
    .nav.nav-tabs{
      width: 246px;
      margin: 0 22px;
      border:none;
      min-height: 50px;
      @media @extraSmallDevices, @smallDevices, @mediumDevices{
        width: auto;
        margin: 0;
      }
      li{
        border:none;
        width: 50%;
        &:first-child{
          a{
            border-right: 1px solid #ededed;
            border-radius:0 ;
          }
        }
        a{
          border:none;
          text-align: center;
          &.active{
            font-weight: @bold;
            color:@theme-primary-color !important;
          }
        }
      }
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      display: flex;
      align-items: center;
    }
  }
}
#book-read-material .tab-content > .tab-pane {
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    //min-height: 100%;
  }
}
.sharethis-inline-share-buttons{
  display: none !important;
}
.tab-content{
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    .container{
      padding: 0;
      #htmlreadingcontent{
        padding: 0 !important;
        margin: 0;
        .main-wrapper{

        }
      }
    }
    .setname-wrapper p{
      display: none;
    }
  }

}
.annotator-adder{
  button{
    white-space: nowrap;
    &:last-child{
      border-left: none !important;
    }
  }
}
.subMenu {
  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    margin-top: 0 !important;
  }
}
.mobChapname{
  border-bottom: 0;
  span{
    font-size: 20px !important;
  }
}
.all-container {
  .container-wrapper:last-child {
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      margin-bottom: 3rem;
    }
  }
}

//#htmlreadingcontent {
//  > div {
//    > div {
//      > div {
//        &.d-flex.flex-wrap {
//          .ws-progressbar {
//            @media @iPhone,@iPad-portrait {
//              margin-top: 2rem !important;
//            }
//          }
//        }
//      }
//    }
//  }
//}


.all-container .container-wrapper .media i{
  background: none;
}

#addedTodo{
  .modal-header{
    border: none;
  }
  .modal-footer {
    border: none;
    .close-btn{
      background: @green;
      border-radius: 5px;
      color:@white;
    }
  }
  .modal-body{
    text-align: center;
    i{
      color:@green;
      font-size: 38px;
    }
    p{
      color:@green;
      font-size: 14px;
      margin-top: 10px;
      span{
        font-weight: @bold;
      }
    }

  }
}
.watch{
  width:50%;
  background: #F4F5FA;
  border-radius: 0 0 5px 5px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color:@theme-primary-color;
  span{
    border-right: 1px solid @theme-primary-color;
    width: 100%;
  }
}
.listen{
  .watch;
  span{
    border-right: none;
  }
}
.notes-by-user {
  li {
    padding: 10px;
    .comment-bg {
      background: rgba(255, 255, 10, 0.3);
      font-size: 16px;
      font-weight: @regular;
    }
  }
}
.chapter-notes{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#htmlreadingcontent {
  embed {
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      width: 400px !important;
    }
  }

}
#allnotes-content{
  .mynotes {
    h3 {
      margin-top: 1rem;
      margin-bottom: 8px !important;
      color: lighten(@dark-gray,25%);
      font-size: 16px;
      font-weight: @bold;
      text-align: left !important;
      text-transform: capitalize;
      margin-left: 1rem;
    }
  }
}
.btn-flashcard{
  border: 1px solid @cyan;
  box-sizing: border-box;
  box-shadow:0 0 10px @gray-light-shadow;
  -webkit-box-shadow:0 0 10px @gray-light-shadow;
  -moz-box-shadow:0 0 10px @gray-light-shadow;
  border-radius: 5px;
  font-size: 12px;
  color:@cyan;
  display: flex;
  align-items: center;
  background: transparent;
  i{
    font-size: 16px;
    margin-right: 5px;
  }
  &.print{
    color:@theme-primary-color;
    border: 1px solid @theme-primary-color;
    i{
      margin-left: 5px;
      color:@theme-primary-color;
      font-size: 16px;
    }
    @media @extraSmallDevices, @smallDevices, @mediumDevices{
      display: none;
    }
  }
}

#backfromAllnotes,#backfromChapternotes,#backChapternotes,.backFromRead{
  background: none;
  color:@theme-primary-color;
}
#backfromAllnotes{
  @media @extraSmallDevices, @smallDevices, @mediumDevices{
    visibility: hidden;

  }
}
#chapter-all-action{
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.modal#PlayAudiOnlyModal {
  background: @loader-color !important;
}

#the-canvas{
  max-width: 768px;
  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    width: 280px;
  }
}


.bookTemplate .content-wrapper .price-wrapper {
  .preview-book-btns {
    .btn-book-buy {
      background: @theme-primary-color;
      &:focus {
        background: @theme-primary-color !important;
      }
    }
  }
  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    .preview-book-btns {
      margin-left: 0;
      .btn-book-buy {
        width: 140px;
      }
    }
  }
}
.bookTemplate .read-content {
  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    margin-bottom: 100px;
  }
}

.book-title{
  padding: 0 1rem;
}
.annotator-adder{
  background: @blue;
  border-radius: 20px;
  border:none;
  button.annotate-btn{
    color:@white;
    border-right: 1px solid @light-gray !important;
    &:last-child{
      border-right:none !important;
    }
  }
}
.annotator-notice{
  display: none !important;
}
.footer-menu-popover .modal-dialog .modal-content{
  box-shadow: none;
  background: none;
}
.annotator-adder, .annotator-outer, .annotator-notice{
  z-index: 99 !important;
}
.annotator-touch-widget-inner .annotator-button{
  width: auto;
}
.annotator-touch-controls{
  background:@blue;
  border-radius: 20px;
  border:none;
}
.annotator-button.annotator-focus:first-child,.annotator-touch-widget-inner .annotator-button{
  border-right: 1px solid @white;
  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    width: 33.33%;
  }
}
.annotator-touch-widget-inner > .annotator-button{
  color:@white !important;
}
.annotator-touch-editor{
  @media @extraSmallDevices, @smallDevices{
    top: -930px !important;
  }
}

#footer-menu-popover {
  z-index: 999;
}
.book-expiry{
  background: @light-green;
  border-radius: 4px;
  width: 246px;
  margin: 0 22px;
  margin-bottom: 1rem;
  height: 70px;
  padding: 0 1rem;
  i{
    color:@white;
    font-size: 36px;
  }
  p{
    color:@white;
    font-size: 10px;
    span{
      font-size: 14px;
    }
  }
}
