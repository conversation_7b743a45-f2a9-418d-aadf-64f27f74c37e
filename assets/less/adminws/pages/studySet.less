@import "_common";

 .studySet {
   padding: 3rem;
   background-color: #F3F7FA !important;
   border: 1px solid #dee2e6 !important;
   box-shadow: 0 2px 10px #e9eef5 !important;

   .study-set-main,.study-set-textarea{
     background-color: @white !important;
   }

.invalid-feedback{
  margin-top: 0 !important;
  font-size: 14px;
}
   .study-set-wrapper-continer {
     margin-top: 10px;
   }
   #revisionTitle{
     width: 100vh !important;
   }
.termAndDef{
  margin-left: 0 !important;
}
   .termAndDef{
     &:hover{
       border-color: @gray !important;
       cursor: pointer;
     }
   }
   .study-set-item {
     margin: 0 !important;
     padding: 0 !important;
   }
   .term-counter {
     &:before {
      left: 0 !important;
     }
   }
   @media @extraSmallDevices, @smallDevices {
     .studySet {
       padding: 1rem !important;
     }
   }
   @media @extraSmallDevices, @smallDevices,@mediumDevices {
     #revisionTitle{
       width: 40vh !important;
     }

   }
   @media @extraSmallDevices {
     .termAndDef {
       width: 40vh !important;
     }
     .form-group{
       margin-bottom: 0 !important;
     }
     .term-counter {
       &:before {
         bottom: 20px !important;
       }
     }
     .add-study-card-btn-wrapper{
       flex-wrap: wrap !important;
       display: flex !important;
     }
     .add-study-card-btn{
       margin-right: 0 !important;
       width: 100% !important;
       margin-bottom: 1rem !important;
     }
   }
   @media @mediumDevices {
     .termAndDef {
       width: 20vh !important;
     }
   }
 }

.sage-body {
  h4.whitetext {
    margin-top: 2rem !important;
  }
  .studySet {
    width: 100% !important;
    .study-set-item {
      padding: 16px 24px 16px 48px !important;
    }
    .invalid-feedback {
      color: @red;
      margin-bottom: 20px;
    }
  }
}




