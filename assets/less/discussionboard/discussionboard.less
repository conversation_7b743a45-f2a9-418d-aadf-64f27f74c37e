@import "../variables/responsive.less";
@import "../wonderslate/common.less";
@import "../wonderslate/color.less";
@theme-gradient:radial-gradient(101.2% 1423.13% at -1.2% 28.5%, rgba(205, 62, 129, 0.89) 0%, rgba(135, 43, 164, 0.89) 100%);
@theme:#9A309B;
@theme1:#9A309B;
@white:#ffffff;
@theme-bg: #949494;
@theme-blue:#9A309B;

body{
  margin: 0 !important;
  padding:0 !important;
}
a,p,h4,button{
  font-family: 'Poppins', sans-serif !important;
  //margin:0;
}
.myflex{
  display: flex;
  align-items: center;
  justify-content: center;
}
.admin-wrapper{
  min-height: 100vh;
  //background: fade(@theme-bg,30%);
}
.discussion-menu {
  margin-top: 1rem;
  &.nav-pills {
    .nav-item {
      margin-bottom: 1rem;
      text-align: center;
      .nav-link {
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        background: @white !important;
        color: @theme1;

        &.active {
          background: @theme-gradient !important;
          color: @white;
        }

        &:focus {
          color: @white;
        }
      }
    }
  }
}
.line{
  position: absolute;
  left: 0;
  top: 1rem;
  img{
    height: 140px;
  }
}
.tab-content {
  .row {
    >.col-12 {
      //@media (max-device-width: 320px) {
      //  padding: 0;
      //}
    }
  }
  @media @iPhone,@iPad-portrait{
    //padding: 10px;
  }
}


.discussion-card{
  box-sizing: border-box;
  padding: 1rem;
  padding-top: 0.5rem;
  background: transparent;
  border:none;
  width: 85%;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.05);
  border-radius: 10px;
  @media @iPhone,@iPad-portrait,@iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{
    padding: 10px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.10);
  }
  //@media screen and (device-height: 568px) and (orientation: portrait) and (-webkit-min-device-pixel-ratio: 2) {
  // // width: 85%;
  // //margin: 0 auto;
  //}
  //@media screen and (device-height : 568px) and (device-width : 320px) and (-webkit-min-device-pixel-ratio: 2){
  //  width:290px;
  //  margin: 0 auto;
  //}
img{
    width: auto;
  max-width: 290px;
    max-height: 200px;
  }

  &:last-child{
    @media @iPhone,@iPad-portrait,@iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{

    }
  }
  //@media @iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{
  //  padding: 5px;
  //  width: 100%;
  //}
  //@media (max-device-width: 320px){
  //  width: 90%;
  //  margin: 0 auto;
  //}
  ol{
    background: transparent;
    margin: 0;
    border-radius: 0;
    padding: 5px 0;
    margin-bottom: 10px;
    span{
      font-style: normal;
      font-weight: normal;
      font-size: 10px;
      color:rgba(68, 68, 68, 0.48);
    }
    li{
      padding: 0;
      &.breadcrumb-item{
        padding: 0;
      }
      a{
        color:@theme1;
        font-size: 12px;
      }
      &::after{

      }
    }
    .breadcrumb-item+.breadcrumb-item::before{
      content:'>';
      font-size: 10px;
      padding: 3px;
      top: -1px;
      position: relative;
      color:@theme1;
      //.gradientText;
    }
  }
}
.q-question{
  color:@modal-text;
}
.content{
  >p{
    color:#444444;
  }
}
.profile{
  display: flex;
  align-items: center;
  img{
    width: 28px;
    height: 28px;
  }
  h4{
    font-size:12px;
    margin-left: 10px;
    font-weight: normal;
  }
  p{
    font-size: 10px;
    color:rgba(68, 68, 68, 0.48);
    margin-left: 5px;
  }
}
.card-actions{
  display: flex;
  justify-content: space-between;
  margin-top: 1rem;
  margin-bottom: 1rem;
  @media @iPhone,@iPad-portrait{
    padding: 0.4rem 0;
  }
  button{
    background: transparent;
    outline: 0;
    border:none;
    display: flex;
    align-items: center;
    font-size: 12px;
    color:rgba(68, 68, 68, 0.48);
    white-space: nowrap;
   // height: 20px;
    @media @iPhone,@iPad-portrait{
      font-size: 10px;
    }
    i{
      font-size: 14px;
      color:rgba(68, 68, 68, 0.48);
      &.circle {
        border-radius: 50px;
        width: 24px;
        height: 24px;
        border: 1.25px solid rgba(68, 68, 68, 0.48);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        margin-right: 5px;
        &.bord{
          border-color:@modal-text;
        }
      }
    }
    &.drop-menu{
      background: rgba(68, 68, 68, 0.2);
      justify-content: center;
      border-radius: 4px;
      i{
        margin-right: 0;
      }
    }

  }
  .dropdown-toggle::after{
    border: none;
  }
  .dropdown-menu{
    background: @white;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    border: none;
    i{
      font-size: 18px;
      margin-right: 10px;
      //.gradientText;
      color:@theme1;
    }
    >li{
      padding: 0.3rem 0.5rem;
      &:hover{
        background: rgba(0, 0, 0, 0.08);
      }

        a{
          cursor: pointer;
          color: #ED2F2F;
          i{
            color: #ED2F2F;
            -webkit-text-fill-color: #ED2F2F;
          }
        }

      >a{
        &:hover{
          background: none;
        }
        display: flex;
        align-items: center;
      }
      &.notcorrect{
        >a{
          i{
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
            transform: rotate(180deg);
          }
        }
      }
    }
  }

}
.card-actions button.answer{
  background: #FFFFFF;
  border: 1.5px solid @theme1;
  box-sizing: border-box;
  border-radius: 5px;
  color:@theme1;
  font-weight: normal;
  height: 24px;
  i{
    color: @theme1;
    margin-right: 10px;
  }
  .myflex;
}
.divider{
  background: url('../../images/discussionboard/line-horiz.svg') center center no-repeat;
  height: 2px;
  margin: 0 auto;
}
.moderate-btns{
  //@media @iPhone,@iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{
  //  display: flex;
  //  margin-top: 1rem;
  //  margin-bottom: 1rem;
  //}
  button{
    display: block;
    border: none;
    width: 75px;
    height: 75px;
    border-radius: 50px;
    background:@theme-gradient;
    box-shadow: 0px 0px 10px rgba(95, 95, 95, 0.15);
    font-size: 9px;
    color: @white;
    outline: 0;
    margin: 10px auto;
    &.delete{
      border: 1px solid #AA3493;
      background: @white;
      color:#ED2F2F;
      i{
        color:#ED2F2F;
      }
    }
    &:focus{
      outline: none;
    }
    i{
      display: block;
      margin-bottom: 5px;
    }

  }
}
.pagination {
  display: flex;
  align-items: center;
  li {
    margin-right: 10px;
    &.disabled{
      background: none;
      a{
        &.actions{
          color:#b4b4b4;
        }
      }
    }
    a {
      font-size: 18px;
      text-align: center;
      span {
        display: block;
      }
      background: none;
      color:#000;
      &.actions{
        font-size: 8px;
        span{
          font-size: 18px;
        }
      }
      &.page-link{
        border-radius: 0;
        border: none;
        &:hover{
          background: none;
        }
        &:focus{
          background: @theme-gradient;
        }
      }
    }
    &.active{
      a{
        background: @theme-gradient;
        width: 33px;
        height: 33px;
        border-radius: 50px;
        .myflex;
      }
    }
  }
}
.confirm,.addtag{
  background: none;
  //.gradientText;
  color:@theme1;
  font-size: 10px;
  position: absolute;
  right: 5px;
  border: none;
  display: flex;
  align-items: center;
  i{
    font-size: 14px;
    //.gradientText;
    color:@theme1;
  }
}
.breadcrumb{
  display: flex;
  align-items: center;
  min-height: 36px;
  >span{
    display: block;
    margin-right: 5px;
  }
  select{
    background: @theme1;
    color:@white;
    border-radius: 4px;
    border: none;
    margin-right: 5px;
    font-size: 12px;
    height: 26px;


  }
}
.sub-name{
  font-size: 12px;
  color:@theme-bg;
}
.addDoubts{
  font-size: 12px;
  background: none;
  border:none;
  margin-right: 5px;
  &:focus{
    outline:none;
  }
  &:hover{
    color:@modal-text;
  }
}
.dropup{
  li{
    a{
      font-size: 12px;
      &:hover{
        text-decoration: none;
      }
    }
  }
}
.answer-card {
  min-height: 290px;
  width:100%;
  padding: 1rem;
  border:none;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  background: transparent;
  border-radius: 10px;
  @media @iPhone,@iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15);
  }
  .answer-textarea {
     min-height: 210px;
    border:none;
    border: 1px solid rgba(68, 68, 68, 0.1);
    //@media @iPhone,@iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{
    //  min-height: 320px;
    //}
    &:focus{
      outline:0;
    }
  }
  .answer-actions{
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
    button{
      margin-right: 15px;
      i{
        color:@theme-bg;
      }
      background: none;
      border:none;
      &.post{
        width: 275px;
        background: none;
        text-transform: uppercase;
        color:@modal-text;
        min-height: 45px;
        .myflex;
        border: 1.25px solid @modal-text;
      }
      &.cancels{
        width: 275px;
        background: @white;
        text-transform: uppercase;
        min-height: 45px;
        border:1px solid @cancel;
        color:@cancel;
        .myflex;
      }
    }
  }
}
.postmodal{
  text-align: center;
  position: absolute;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  &.fade:not(.show){
    opacity: 1;
  }
  margin: 0 auto;
  width: 319px;
  height: 190px;
  margin-top: 4rem;
  .modal-content{
    border: none;
  }
  h4 {
    font-size: 14px;
   //.gradientText;
    color:@theme1;
    margin-top: 1rem;
    font-weight: normal;
   }
    p{
      font-size: 10px;
      color:@theme-bg;
    }

.modal-dialog{
  transition: none !important;
  transform: none !important;
  margin: 0;
}
}
.modalBackdrop{
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100%;
  height: 100%;
  background-color: #000;
  opacity: 0.5;
  border-radius: 4px;
  display: none;
}
.btn-dismiss{
  margin-top: 1rem;
  background: @theme1;
  color:#fff;
  font-size: 14px;
}
.circle_around{
  border: 3px solid @theme1;
  border-radius: 50px;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  margin-top: 1rem;
  .myflex;
  i{
    color:@theme1;
    font-weight: bold;
  }

}
.answer-box{
  background: fade(@theme-bg,10%);
  width: 90%;
  border: none;
  min-height:40px;
  font-size: 14px;
}
.category-modal{
  background: radial-gradient(156.52% 3032.45% at -2.67% 0%, #8A2CA3 20.31%, #CA3D82 100%) !important;
  box-shadow: 0px -4px 10px rgba(0, 0, 0, 0.1);
  border-radius: 10px 10px 0px 0px;
  display: none;
  position: absolute;
  &.fade:not(.show){
    opacity: 1;
  }
  margin: 0 auto;
  margin-top: 4rem;
  .modal-content{
    border: none;
    background: transparent;
    min-height: 200px;
    margin-top: 4rem;
  }
  h4 {
    font-size: 14px;
    color:@white;
    font-weight: normal;
  }
  p{
    font-size: 10px;
    color:@white;
    margin-top: 0.5rem;
  }

  .modal-dialog{
    transition: none;
    transform: none;
    margin: 0;
  }
  .modal-footer{
    border:none;
    .skip{
      .reset-btn;
      background: @white;
      box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
      width: 100%;
      height: 50px;
      color:#6D2AB8;
      border-radius: 5px;
    }
  }
  .filter{
    border-color:@white !important;
    i{
      color:@white;
    }
  }
}
.solved{
  position: absolute;
  right: 20px;
  color:#379B5B;
  background: #D4F1DF;
  border-radius: 50px;
  width: 65px;
  font-size: 14px;
  text-align: center;
  display: block;
}
.userAnswer{
  background: fade(@theme-bg,10%);
  margin-left: 10px;
  margin-top: 1rem;
  padding: 0.5rem;
  border-radius: 4px;
  img{
    max-width: 350px;
    @media @iPhone,@iPad-portrait{
      max-width: 290px;
    }
  }
  input{
    background: transparent;
  }
}
.answerContent{
  .card-actions{
    padding: 0 0.5rem;
    button{
      font-size: 10px;
    }
  }
}
.answer-head{
  font-size: 14px;
  text-transform: uppercase;
  color:@theme-bg;
  margin-bottom: 1rem;
}
#mobileque-modal{
  .category-modal{
    display: block;
    position: fixed;
    .modal.fade .modal-dialog{
      transition: none !important;
    }
  }
  .modalBackdrop{
    position: fixed;
  }
  .postmodal{
    position: fixed;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
  }

}

.textOverflow{
  max-width: 98%;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.typeahead.dropdown-menu {
  max-width:349px;
  min-width: 349px;
  li a{
    max-width: 98%;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
  }
}
.taggs{
  display: block;
  margin-right: 10px;
}
.ans-modal-img-remove{
color:#ED2F2F;
  border:#ED2F2F;
  border:1px solid #ED2F2F;
}
.searchbar{
  .typeahead.dropdown-menu{
    max-width: 95%;
    min-width: 95%;
  }
}
.admin-wrapper{
  .container-fluid{
    margin: 0 4rem;
    //@media @iPhone,@iPhone5-portrait,@iPhone6-portrait,@iPhone7-portrait,@iphoneX-portrait{
    //  margin:0 0;
    //}
  }
}
select{ font-size: 16px !important; }
.answer-drop{
  .drop-menu{
    background: none;
    border:none;
    color:@modal-text;
    &:focus{
      outline:none;
    }
  }
  .dropleft .dropdown-toggle::before{
    display: none;
  }
  .dropdown-menu{
    background: @white;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    border: none;
    i{
      font-size: 18px;
      margin-right: 10px;
      //.gradientText;
      color:@theme1;
    }
    >li{
      padding: 0.3rem 0.5rem;
      &:hover{
        background: rgba(0, 0, 0, 0.08);
      }

      a{
        cursor: pointer;
        color: #ED2F2F;
        i{
          color: #ED2F2F;
          -webkit-text-fill-color: #ED2F2F;
        }
      }

      >a{
        &:hover{
          background: none;
        }
        display: flex;
        align-items: center;
      }
      &.notcorrect{
        >a{
          i{
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
            transform: rotate(180deg);
          }
        }
      }
    }
  }
  .dropdown, .dropleft, .dropright, .dropup{
    top:3px;
  }
}
.addtodoubts{
  position: absolute;
  display: flex;
  align-items: center;
  right:10px;
  button{
    color: rgba(68, 68, 68, 0.48);
  }
  .drop-menu{
    background: none;
    border:none;
    color:@modal-text;
    &:focus{
      outline:none;
    }
  }
  .dropleft .dropdown-toggle::before{
    display: none;
  }
  .dropdown-menu{
    background: @white;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.08);
    border-radius: 5px;
    border: none;
    i{
      font-size: 18px;
      margin-right: 10px;
      //.gradientText;
      color:@theme1;
    }
    >li{
      padding: 0.3rem 0.5rem;
      &:hover{
        background: rgba(0, 0, 0, 0.08);
      }

      a{
        cursor: pointer;
        color: #ED2F2F;
        i{
          color: #ED2F2F;
          -webkit-text-fill-color: #ED2F2F;
        }
      }

      >a{
        &:hover{
          background: none;
        }
        display: flex;
        align-items: center;
      }
      &.notcorrect{
        >a{
          i{
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
            transform: rotate(180deg);
          }
        }
      }
    }
  }
  .dropdown, .dropleft, .dropright, .dropup{
    top:3px;
  }
}
.flex-action{
  display: flex;
  align-items: center;
  min-height: 24px;
  #share-button{
    border-left: 0.5px solid rgba(68, 68, 68, 0.48);
  }
}
#cke_questionText{
  border-radius: 10px;
  border:none;
}
.reset-padding{
  @media @iPhone,@iPad-portrait{
    padding: 0;
  }
}
.tabs{
  margin: 0 auto;
  @media @iPhone,@iPad-portrait{

  }
}
#myDoubtsTab,#myAnswerTab,#alldoubts{
  >p{
    margin-top: 1rem;
    padding: 5px;
  }
}
.reset-app #myDoubtsTab,#myAnswerTab,#alldoubts{
  >p{
    margin-top: 7rem;
    padding: 5px;
  }
}