var image_medal='';
var image_won='';
var image_lose='';
var image_tie='';
var image_badge='';
var setSound;
function playAgain(){
    if(source=='android'){
        JSInterface.restartGame(nextChallenger,nextchallengerPlace);
    }
    else if(source=='ios'){
        webkit.messageHandlers.restartGame.postMessage('');
    }
}
function backToHome(){
    if(source=='android'){
        JSInterface.backToHome();
    }
    else if(source=='ios'){
        webkit.messageHandlers.backToHome.postMessage('');
    }
}
function audioChange(){
    if(quizMode==''){
        $('.audioChange i').text(($('.audioChange i').text() == 'volume_up') ? 'volume_off' : 'volume_up');
        if(source=='android'){
            JSInterface.audioChange();
        }
        else if(source=='ios'){
            webkit.messageHandlers.audioChange.postMessage('');
        }
    }
}
function audioChange() {
    if(quizMode==''){
        $('.audioChange i').text(($('.audioChange i').text() == 'volume_up') ? 'volume_off' : 'volume_up');
        if ($('.audioChange i').text() == 'volume_up') {
            setSound = true;
        } else {
            setSound = false;
        }
        if(source=='android'){
            JSInterface.audioChange(setSound);
        }
        else if(source=='ios'){
            webkit.messageHandlers.audioChange.postMessage(setSound);
        }
    }
}
function userShowAudio(file){
    if(quizMode==''){
        if(source=='android'){
            JSInterface.userAudio();
        }
        else if(source=='ios'){
            webkit.messageHandlers.userAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                opponentFound = new Audio(file);
                opponentFound.play();
            }
        }

    }
}
function slidingAudio(file) {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.slideBotAudio();
        } else if (source == 'ios') {
            webkit.messageHandlers.slideBotAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                opponentScroll = new Audio(file);
                opponentScroll.play();
            }
        }
    }
}
function battleAudios(file){
    if(quizMode==''){
        if(source=='android'){
            JSInterface.battleAudio();
        }
        else if(source=='ios'){
            webkit.messageHandlers.battleAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                gameBackgroundMusic = new Audio(file);
                gameBackgroundMusic.play();
                gameBackgroundMusic.loop = true;
            }
        }
    }
}
function queAudio(file) {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.questionAudio();
        } else if (source == 'ios') {
            webkit.messageHandlers.questionAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                questionDisplay = new Audio(file);
                questionDisplay.play();
            }
        }
    }
}
function botCorrectAudio(file) {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.botCorrect();
        } else if (source == 'ios') {
            webkit.messageHandlers.botCorrect.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                botCorrect = new Audio(file);
                botCorrect.play();
            }
        }
    }
}
function botIncorrectAudio(file){
    if(quizMode==''){
        if(source=='android'){
            JSInterface.botIncorrect();
        } else if(source=='ios'){
            webkit.messageHandlers.botIncorrect.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                botIncorrect = new Audio(file);
                botIncorrect.play();
            }
        }
    }
}
function timeAudio(file){
    if(quizMode==''){
        if(source=='android'){
            JSInterface.timerAudio();
        } else if(source=='ios'){
            webkit.messageHandlers.timerAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                timeUp = new Audio(file);
                timeUp.play();
            }
        }
    }
}
function answerCorrectAudio(file){
    if(quizMode==''){
        if(source=='android'){
            JSInterface.answerCorrect();
        }
        else if(source=='ios'){
            webkit.messageHandlers.answerCorrect.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                userCorrect = new Audio(file);
                userCorrect.play();
            }
        }
    }
}
function answerInCorrectAudio(file){
    if(quizMode==''){
        if(source=='android'){
            JSInterface.answerIncorrect();
        }
        else if(source=='ios'){
            webkit.messageHandlers.answerIncorrect.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                userIncorrect = new Audio(file);
                userIncorrect.play();
            }
        }
    }
}
function submitCallback(qaObj){
    if(source=='android') {
        JSInterface.checkConnection();
    }
    else {
        webkit.messageHandlers.submitQaData.postMessage(qaObj);
    }
}
function submitFromAndroid(){
    var saveData = $.ajax({
        type: 'POST',
        crossdomain: true,
        url:submitUrl,
        contentType: "application/json",
        dataType: "json",
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type':'application/json',
            'Access-Control-Allow-Methods': 'GET, DELETE, HEAD, OPTIONS',
            'X-Auth-Token': tokenId,
            'Content-Encoding': 'gzip'
        },
        data: qaObj,
        success:function (response){
            if (response.status == 'success') {
                $('.loader-submit').hide();
                if(quizMode =='') {
                    $('#winnerModal').modal('hide');
                    currentBadge(response);
                    $('.resultWrapper').removeClass('d-none');
                }
                else if(quizMode =='practice' || quizMode =='test' || quizMode =='testSeries'){
                    $('.quizes,.quiz-profile').removeClass('d-flex').addClass('d-none');
                    $('.practice-result').removeClass('d-none').addClass('d-flex');
                    $('.nav-tabs a[href="#all"]').tab('show');
                    showAllAnswers();
                    incorrectAnswers();
                    correctAnswers();
                    skippedAnswers();
                    currentBadge(response);
                }
                if(quizMode =='test'){
                    $('#practice-summary').text('Test Summary');
                }
            }
        }
    });
}
//ios only
function recieveScore(data){
    $('#winnerModal').modal('hide');
    $('.resultWrapper').removeClass('d-none');
    currentBadge(data);
}
//ios only
function imageJsonSave(){
    image_won='https://assets1.lottiefiles.com/packages/lf20_sonmjuir.json';
    image_lose='https://assets5.lottiefiles.com/private_files/lf30_ovxvpeuq.json';
    image_tie='https://assets5.lottiefiles.com/packages/lf20_pthbdqkh.json';
    image_medal='https://assets6.lottiefiles.com/packages/lf20_mntsz64q.json';
    image_badge='https://assets10.lottiefiles.com/packages/lf20_i6sqnxav.json';
}
function botImages(){
    if(quizMode==''){
        if(source=='android') {
            $(".bot-profile-img").attr({"src": 'file:///android_asset/quiz/assets/images/prepjoy/' + botImage + '.jpg'});
        }
        else{
            $(".bot-profile-img").attr({ "src": +botImage+'.jpg' });
        }
    }
}
function stopAudios() {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.stopAudio();
        } else if (source == 'ios') {
            webkit.messageHandlers.stopAudio.postMessage('');
        }else if(source=='web'){
            //pausing ongoing audio
            if (gameSound){
                gameBackgroundMusic.pause();
            }
        }
    }
}
function winAudios(file) {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.winAudio();
        } else if (source == 'ios') {
            webkit.messageHandlers.winAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                win = new Audio(file);
                win.play();
            }
        }
    }
}
function tieAudios(file) {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.tieAudio();
        } else if (source == 'ios') {
            webkit.messageHandlers.tieAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                tie = new Audio(file);
                tie.play();
            }
        }
    }
}
function loseAudios(file) {
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.loseAudio();
        } else if (source == 'ios') {
            webkit.messageHandlers.loseAudio.postMessage('');
        }else if(source=='web'){
            if (gameSound){
                lose = new Audio(file);
                lose.play();
            }
        }
    }
}
function userWinsImage() {
    if(quizMode==''){
        if (source == 'ios') {
            animdata.path = image_won;
        } else if (source == 'android') {
            animdata.path = 'file:///android_asset/quiz/assets/images/prepjoy/win.json';
        }
    }
}
function gameTieImage() {
    if(quizMode==''){
        if (source == 'ios') {
            animdata.path = image_tie;
        } else if (source == 'android') {
            animdata.path = 'file:///android_asset/quiz/assets/images/prepjoy/tie.json';
        }
    }
}
function gameLoseImage() {
    if(quizMode==''){
        if (source == 'ios') {
            animdata.path = image_lose;
        } else if (source == 'android') {
            animdata.path = 'file:///android_asset/quiz/assets/images/prepjoy/lose.json';
        }
    }
}
function shareQuiz(){
    if(quizMode==''){
        if (source == 'android') {
            JSInterface.shareQuiz();
        } else if (source == 'ios') {
            webkit.messageHandlers.shareQuiz.postMessage('');
        }
    }
}
