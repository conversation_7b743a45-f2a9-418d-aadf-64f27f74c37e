<!DOCTYPE html>
<html>
<head>
    <title>PDF Text Extraction</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.8.335/pdf.js"></script>
</head>
<body>
<h1>PDF Text Extraction</h1>

<!-- Input to select a PDF file -->
<input type="file" id="pdfInput">

<!-- Placeholder for PDF rendering -->
<div id="pdfViewer"></div>

<!-- Display extracted text here -->
<div id="textOutput"></div>

<script>
    document.getElementById('pdfInput').addEventListener('change', function (event) {
        const file = event.target.files[0];
        const pdfViewer = document.getElementById('pdfViewer');
        const textOutput = document.getElementById('textOutput');

        // Initialize PDF.js
        const fileReader = new FileReader();

        fileReader.onload = function () {
            const arrayBuffer = this.result;

            const loadingTask = pdfjsLib.getDocument({ data: arrayBuffer });

            loadingTask.promise
                .then(pdfDoc => {
                    const numPages = pdfDoc.numPages;
                    const pagePromises = [];

                    // Function to process a single page
                    function processPage(pageNumber) {
                        return pdfDoc.getPage(pageNumber)
                            .then(page => {
                                return page.getTextContent();
                            })
                            .then(textContent => {
                                const textItems = textContent.items;
                                let pageText = '';

                                for (let i = 0; i < textItems.length; i++) {
                                    pageText += textItems[i].str + ' ';
                                }

                                return pageText;
                            });
                    }

                    // Create an array of promises for processing each page
                    for (let i = 1; i <= numPages; i++) {
                        pagePromises.push(processPage(i));
                    }

                    // Use Promise.all to wait for all page promises to resolve
                    return Promise.all(pagePromises);
                })
                .then(allPageTextArray => {
                    const allPageText = allPageTextArray.join(' '); // Combine text from all pages
                    textOutput.textContent = allPageText;
                })
                .catch(error => {
                    console.error('Error loading or processing PDF:', error);
                });
        };

        fileReader.readAsArrayBuffer(file);
    });
</script>
</body>
</html>
