<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${booksMst?.title ?: 'Book'} - Overview</title>

    <!-- KaTeX for math rendering - Load synchronously for Puppeteer -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

    <style>
        /* Print-optimized CSS */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Noto Sans", "Segoe UI", "Roboto", sans-serif;
            font-size: 1em;
            line-height: 1.6;
            color: #000;
            margin: 2cm 1.5cm;
            padding: 0;
            background: white;
        }

        /* Page break controls */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        .page-break-inside-avoid {
            page-break-inside: avoid;
        }

        /* Typography */
        .book-title {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        .section-heading {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h1 {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        h2 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h3 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        p {
            margin: 0.8em 0;
            text-align: justify;
        }

        /* Book Overview Section */
        .book-overview {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0.5em;
            padding: 1.5em;
            margin: 2em 0;
            page-break-inside: avoid;
        }

        .overview-title {
            font-size: 1.3em;
            font-weight: bold;
            margin: 0 0 1em 0;
            color: #333;
            text-align: center;
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 20px;
        }

        .overview-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            text-align: left;
            border: 2px solid transparent;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .overview-item-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            color: white;
            flex-shrink: 0;
        }

        .overview-item-content {
            flex: 1;
        }

        .overview-item-title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #333;
            font-size: 1rem;
        }

        .overview-item-count {
            font-size: 0.9rem;
            color: #666;
        }

        /* Icon Colors */
        .icon-exercise { background: #4CAF50; }
        .icon-long-answer { background: #2196F3; }
        .icon-short-answer { background: #FF9800; }
        .icon-very-short { background: #9C27B0; }
        .icon-assertion { background: #607D8B; }
        .icon-problem { background: #795548; }
        .icon-mcq { background: #F44336; }
        .icon-fill-blank { background: #3F51B5; }
        .icon-true-false { background: #009688; }
        .icon-match { background: #E91E63; }
        .icon-sequence { background: #FF5722; }

        /* Chapter Cards */
        .chapter-overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }

        .chapter-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 12px;
            border-left: 4px solid #1a237e;
            page-break-inside: avoid;
            margin-bottom: 1em;
        }

        .chapter-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1a237e;
            margin-bottom: 15px;
        }

        .chapter-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: white;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #333;
        }

        .stat-item i {
            color: #1a237e;
        }

        /* Book Summary Section */
        .book-summary-section {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid #dee2e6;
            page-break-inside: avoid;
        }

        .book-summary-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a237e;
            margin-bottom: 20px;
            text-align: center;
        }

        .book-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .book-stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }

        .book-stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        .book-stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #1a237e;
            margin-bottom: 5px;
        }

        .book-stat-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        /* Content sections */
        .content-section {
            margin: 3em 0;
            page-break-inside: avoid;
        }

        .section {
            page-break-before: always;
        }

        .section-header {
            margin-bottom: 2em;
            padding-bottom: 0.5em;
            border-bottom: 1px solid #ccc;
        }

        /* AI Footer */
        .ai-footer {
            margin-top: 4em;
            padding: 2em 0;
            text-align: center;
            border-top: 2px solid #3498db;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            page-break-inside: avoid;
        }

        .ai-footer-content {
            font-size: 1.1em;
            color: #1a237e;
            font-weight: 600;
            margin-bottom: 0.5em;
        }

        .ai-footer-link {
            font-size: 1.2em;
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
            border: 2px solid #3498db;
            padding: 0.5em 1.5em;
            border-radius: 25px;
            display: inline-block;
            background: white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
        }

        .ai-footer-link:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .ai-footer-icon {
            margin-right: 0.5em;
            font-size: 1.3em;
        }

        /* Print media queries */
        @media print {
            body {
                font-size: 0.9em;
                margin: 2cm 1.5cm;
                padding: 0;
            }
            
            .page-break-before {
                page-break-before: always;
            }
            
            .page-break-after {
                page-break-after: always;
            }
            
            .page-break-inside-avoid {
                page-break-inside: avoid;
            }
        }

        /* No data message */
        .no-data-message {
            text-align: center;
            padding: 3em 1em;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Book Title -->
    <h1 class="book-title">${booksMst?.title ?: 'Book Overview'}</h1>

    <!-- Book Meta Information -->
    <g:if test="${booksMst?.authors || booksMst?.subjectyear}">
        <div style="text-align: center; margin-bottom: 2em; color: #666;">
            <g:if test="${booksMst?.authors}">
                <span style="margin: 0 15px;">📚 ${booksMst.authors}</span>
            </g:if>
            <g:if test="${booksMst?.subjectyear}">
                <span style="margin: 0 15px;">🎓 ${booksMst.subjectyear}</span>
            </g:if>
        </div>
    </g:if>

    <!-- Book Description -->
    <g:if test="${booksMst?.description}">
        <div class="content-section">
            <h2 class="section-heading">📖 Book Description</h2>
            <p>${raw(booksMst.description?.replaceAll('<br>', '<br/>'))}</p>
        </div>
    </g:if>
