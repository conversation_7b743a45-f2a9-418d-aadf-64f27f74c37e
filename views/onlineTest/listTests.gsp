<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container">
                        <h2 class="text-center">Online Test Centre</h2>
                        <hr/>

                        <!-- Filters -->
                        <form class="form-inline text-center" method="get" action="${createLink(controller:'onlineTest', action:'listTests')}">
                            <input type="hidden" name="instituteId" value="${instituteId}"/>
                            <div class="form-group">
                                <label for="filter"><b>Filter Tests:</b></label>&nbsp;&nbsp;
                                <select name="filter" id="filter" class="form-control">
                                    <option value="">All</option>
                                    <option value="upcoming" <g:if test="${filter=='upcoming'}">selected</g:if>>Upcoming</option>
                                    <option value="ongoing" <g:if test="${filter=='ongoing'}">selected</g:if>>Ongoing</option>
                                    <option value="awaitingResults" <g:if test="${filter=='awaitingResults'}">selected</g:if>>Awaiting Results</option>
                                    <option value="completed" <g:if test="${filter=='completed'}">selected</g:if>>Completed</option>
                                </select>
                                &nbsp;&nbsp;<button type="submit" class="btn btn-primary">Apply</button>
                                <%if("true".equals(""+session["enableTest"])){%> &nbsp;<a href="/test-generator" class="btn btn-info fadein-animated mr-2">Create Test</a><%}%>
                            </div>

                        </form>
                        <br/>

                        <!-- Tests Table -->
                        <table class="table table-striped">
                            <thead>
                            <tr>
                                <th>Test Name</th>
                                <th>Class</th>
                                <th>Start Date/Time</th>
                                <th>End Date/Time</th>
                                <th>Result Date/Time</th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <g:if test="${testList}">
                                <g:each in="${testList}" var="t">
                                    <tr>
                                        <td>${t.name}</td>
                                        <td>${t.batchName}</td>
                                        <td><g:formatDate date="${t.startDateTime}" format="dd-MM-yyyy HH:mm"/></td>
                                        <td><g:formatDate date="${t.endDateTime}" format="dd-MM-yyyy HH:mm"/></td>
                                        <td><g:formatDate date="${t.resultDateTime}" format="dd-MM-yyyy HH:mm"/></td>
                                        <td>
                                            <g:if test="${t.canTakeTest}">
                                                   <!-- This is a placeholder logic. You’d do real checks in the controller or service. -->
                                                <g:link controller="prepjoy" action="prepJoyGame" params="[testId:t.testId, mode:'bookTest',quizType:'testSeries',learn:'false']" class="btn btn-primary btn-sm" target="_blank">Take test</g:link>
                                            </g:if>
                                            <!-- Instructor or manager actions -->
                                            <g:if test="${t.ongoingTest&&("Instructor".equals(t.userType)||"Manager".equals(t.userType))}">
                                                <!-- If upcoming, show delete link and block link -->
                                                <!-- This is a placeholder logic. You’d do real checks in the controller or service. -->
                                                <g:link action="blockStudents" params="[testId:t.testId, instituteId:instituteId,mode:'ongoing']" class="btn btn-info btn-sm">Blocked Students</g:link>
                                            </g:if>
                                            <g:if test="${t.canDelete}">
                                                <!-- If upcoming, show delete link and block link -->
                                                <!-- This is a placeholder logic. You’d do real checks in the controller or service. -->
                                                <g:link action="blockStudents" params="[testId:t.testId, instituteId:instituteId]" class="btn btn-info btn-sm">Block Students</g:link>
                                                <g:link action="deleteTest" params="[testId:t.testId, instituteId:instituteId]" class="btn btn-danger btn-sm">Delete</g:link>

                                            </g:if>
                                            <g:if test="${t.testAlreadyTaken}">
                                                Test Taken
                                            </g:if>
                                            <!-- Manager or instructor can see results after test end -->
                                            <g:if test="${t.showResults}">
                                                <%if((t.userType == "Student"||t.userType==null)){
                                                     if(t.quizRecId!=null && t.quizRecId!=""){
                                                %>
                                                <g:link controller="prepjoy" action="prepJoyGame" params="[quizRecId:t.quizRecId, historyPage:'true',learn:'false']" class="btn btn-info btn-sm" target="'_blank'">Results</g:link>
                                                <%}else{%>
                                                    <span style="color: red">Not attended </span>
                                                <%}}else{%>
                                                <g:link action="testResults" params="[testId:t.testId, batchId:t.batchId]" class="btn btn-info btn-sm">Results</g:link>
                                                   <%}%>
                                            </g:if>

                                            <!-- View Questions button for instructors and managers -->
                                            <g:if test="${t.canSeeQP}">
                                                <g:link action="viewQuestions" params="[testId:t.testId]" class="btn btn-secondary btn-sm">View Questions</g:link>
                                            </g:if>
                                        </td>
                                    </tr>
                                </g:each>
                            </g:if>
                            <g:else>
                                <tr><td colspan="5">No tests found.</td></tr>
                            </g:else>
                            </tbody>
                        </table>

                        <!-- Pagination Controls -->
                        <g:paginate total="${totalCount}" params="${params}"/>

                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
