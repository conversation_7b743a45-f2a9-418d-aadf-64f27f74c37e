package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.*

import com.wonderslate.CreationService
import com.wonderslate.groups.GroupsService
import com.wonderslate.institute.AccessCode
import com.wonderslate.institute.BatchUserDtl
import com.wonderslate.institute.BatchUserDtlNotRegistered
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.log.DeviceInformation
import com.wonderslate.publish.BooksPermission
import com.wonderslate.usermanagement.*
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugins.mail.MailService
import grails.transaction.Transactional
import jxl.Sheet
import jxl.Workbook
import jxl.WorkbookSettings
import grails.plugin.springsecurity.annotation.Secured
import org.apache.commons.io.FileUtils
import grails.converters.JSON
import org.apache.commons.lang.StringUtils
import org.grails.web.util.WebUtils
import org.joda.time.Days
import org.joda.time.LocalDate
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest

import javax.servlet.http.Cookie
import groovy.sql.Sql
import grails.plugins.rest.client.RestBuilder

import org.imgscalr.*
import javax.imageio.ImageIO
import java.awt.image.BufferedImage

import com.ccavenue.security.*

@Transactional
class CreationController {
    def simpleCaptchaService
    String createdBy = "wonderslate"
    QuizExtractorService quizExtractorService
    SpringSecurityService springSecurityService
    UtilService utilService
    UserManagementService userManagementService
    MailManagementService mailManagementService
    DataProviderService dataProviderService
    UserLogService userLogService
    def rememberMeServices

    CreationService creationService
    PrepjoyService prepjoyService
    def redisService
    DataNotificationService dataNotificationService
    GroupsService groupsService
    MailService mailService

    def index() {
        String contentType = "file"
        if("quiz".equals(params.contentType)) contentType = "quiz"
        else if("link".equals(params.contentType)) contentType = "link"
        [contentType:contentType]
    }

    def userProfile() {
        if("update".equals(params.mode)){
            User user = User.findByUsername(springSecurityService.currentUser.username)
            user.name = params.name
            user.mobile = params.mobile
            user.email = params.email
            user.save(failOnError: true,flush: true)
            session['userdetails'] = user
            redirect (controller: 'creation',action: 'userProfile')
        }

        [title:'Edit Profile', newCss : true,hideBanner:true,disciplines:getDisciplines(),showLibrary:utilService.hasLibraryAccess(request,getSiteId(request))]
    }

    def  Integer getSiteId(request){
        Integer siteId = new Integer(1)

        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON

            if(jsonObj.siteId!=null) {
                siteId = new Integer(jsonObj.siteId)
            } else if(params.siteId!=null) {
                siteId = new Integer(params.siteId)
            }
        }

        return siteId
    }


    @Transactional
    def getDisciplines(){
        def sql="SELECT discipline,count(id) FROM books_mst where site_id=9 and discipline is not null and status='published' group by discipline";
        def dataSource = grailsApplication.mainContext.getBean('dataSource')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql);


        List discipline = results.collect{ temp ->
            return [discipline:temp[0],noOfBooks:temp[1]];
        }

        return discipline;
    }

    @Secured(['ROLE_USER'])
    def updateUserPassword(){
        String password,oldPassword
       boolean wrongOldPassword=true

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                password=jsonObj.password
                oldPassword=jsonObj.oldPassword
            } else {
                password=params.password
                oldPassword=params.oldPassword
            }
        }

        User user = User.findByUsername(springSecurityService.currentUser.username)

        if (springSecurityService.passwordEncoder.isPasswordValid(user.password, oldPassword, null)) { //validates raw password against hashed
            if(password != null) {
                wrongOldPassword = false
                user.password  = springSecurityService.encodePassword(password)
                user.save(failOnError: true,flush: true)
            }
        } else {
            println("Error")
        }

        session['userdetails'] = user
        def json = [ 'status': wrongOldPassword?"failed":"success"]
        render json as JSON
    }

    @Secured(['ROLE_USER'])
    def updateMigrateUserPassword(){
        String password
        boolean passwordChanged=true

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                password=jsonObj.password
            } else {
                password=params.password

            }
        }
        User user = User.findByUsername(springSecurityService.currentUser.username)
        if(password!=null && user!=null) {
             user.password = springSecurityService.encodePassword(password)
            user.save(failOnError: true, flush: true)
        } else {
            passwordChanged=false
            println("Error")
        }

        session['userdetails'] = user
        def json = [ 'status': passwordChanged?"success":"failed"]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def updateUserProfile(){
        String name,mobile,email,institution,department,interests,country,address1,city,state,district,twitter,rankDate
        boolean json=false

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                json=true
                def jsonObj = request.JSON
                name=jsonObj.name
                mobile = jsonObj.mobile
                email = jsonObj.email
                institution=jsonObj.institution
                department = jsonObj.department
                interests = jsonObj.interests!=null?String.join(",", jsonObj.interests):""
                country = jsonObj.country

                address1 = jsonObj.address1
                city = jsonObj.city
                state = jsonObj.state
                district = jsonObj.district
                twitter = jsonObj.twitter
                rankDate=jsonObj.rankDate
            } else {
                name=params.name
                mobile=params.mobile
                email=params.email
                institution=params.institution
                department = params.department
                interests = request.getParameterValues("interests")!=null?String.join(",", request.getParameterValues("interests")):""
                country = params.country

                address1 = params.address1
                city = params.city
                state = params.state
                district =params.district
                twitter = params.twitter
            }
        }

        User user = User.findByUsername(springSecurityService.currentUser.username)
        if(name!=null) user.name = name
        if(mobile!=null) user.mobile = mobile
        if(email!=null) user.email = email.startsWith("Google")?email.substring(6):email
        if(institution!=null) user.institution = institution
        if(department!=null) user.department = department
        if(interests!=null) user.interests = interests
        if(country!=null) user.country = country

        if(address1!=null) user.address1 = address1
        if(city!=null) user.city = city
        if(state!=null) user.state = state
        if(district!=null) user.district = district
        if(twitter!=null) user.twitter = twitter

        user.save(failOnError: true,flush: true)
        session['userdetails'] = user
        if (rankDate!=null &&  !"".equals(rankDate)) prepjoyService.getDailyLeaderBoard(rankDate,getSiteId(request))
        if(json){
            render(contentType: 'text/json') {[
                'status': "OK"
            ]}
        } else
            redirect (controller: 'creation' , action: 'userProfile')
    }

    @Secured(['ROLE_USER']) @Transactional
    def uploadProfileImage(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')

        if(file.empty) {
            flash.message = "File cannot be empty"
        } else {
            if("user".equals(params.type)){
                User user = User.findByUsername(springSecurityService.currentUser.username)
                File uploadDir = new File("upload/user/"+user.id)
                if(!uploadDir.exists()) uploadDir.mkdirs()

                //creating directory to process images
                File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
                if(!uploadDir1.exists()) uploadDir1.mkdirs()
                String filename=file.originalFilename
                filename=filename.replaceAll("\\s+","")
                BufferedImage image = ImageIO.read(file.getInputStream())

                ByteArrayOutputStream baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte1 = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte2 = baos.toByteArray()
                baos.close()

                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

                //saving original image finally
                file.transferTo(new File(uploadDir.absolutePath+"/"+file.filename))

                user.profilepic = file.filename
                user.save(failOnError: true,flush: true)
                groupsService.updateGroupUser(user.id)
                session["userdetails"] = user
                Long siteId = null
                siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
                discussionService.updateUserDetails(user.username,siteId)
                redirect(controller: params.sourceController, action:params.source)
            } else if("group".equals(params.type)){
                Groups group = Groups.findById(new Integer(params.groupId))
                File uploadDir = new File("upload/group/"+group.id)
                if(!uploadDir.exists()) uploadDir.mkdirs()
                String filename=file.originalFilename
                filename=filename.replaceAll("\\s+","")
                //creating directory to process images
                File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
                if(!uploadDir1.exists()) uploadDir1.mkdirs()

                BufferedImage image = ImageIO.read(file.getInputStream())

                ByteArrayOutputStream baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte1 = baos.toByteArray()
                baos.close()

                baos = new ByteArrayOutputStream()
                ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
                baos.flush()
                byte[] scaledImageInByte2 = baos.toByteArray()
                baos.close()

                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

                //saving original image finally
                file.transferTo(new File(uploadDir.absolutePath+"/"+file.originalFilename))

                group.profilepic = file.originalFilename
                group.save(failOnError: true,flush: true)
                redirect(controller: "usermanagement", action:"editprofile", params: [groupId:group.id, displayName: group.name])
            }
        }
    }

    @Secured(['ROLE_USER'])
    def userDetails(){
        String email = "",schoolOrCollege = "", mobile = "", country ="",district = "",state="",who=""
        int pincode =0
        User user = User.findByUsername(springSecurityService.currentUser.username)
        schoolOrCollege = user.school != null ? user.school : schoolOrCollege
        country = user.country != null ? user.country : country
        pincode = user.pincode > 0 ? user.pincode : pincode
        if(user.student == "on") who = "student"
        else if(user.teacher == "on") who = "teacher"
        else if(user.parent == "on") who = "parent"
        def json =[
            'name': user.name,
            'email': ("<EMAIL>".equals(user.email)?"":user.email),
            'mobile': ("mobile".equals(user.mobile)?"":user.mobile),
            'profilePic':user.profilepic,
            'id':user.id,
            'state':user.state,
            'district':user.district,
            'schoolOrCollege':schoolOrCollege,
            'country':country,
            'pincode':pincode,
            'who':who
        ]
        render json as JSON
    }

    def changePasswordModal(){}

    @Secured(['ROLE_USER'])
    def topiccreation() {
        TopicMst topicMst
        if(params.topic==null||"".equals(params.topic)){
            flash.error = "Please enter topic"
        } else {
			topicMst =  new TopicMst(topicName: params.topic, syllabus: params.syllabus, grade: params.grade, subject: params.subject,
					createdBy: springSecurityService.currentUser.username, syllabusType: params.syllabusType, country:params.country)
            topicMst.save(failOnError: true)
            utilService.sitemap()
            flash.message = "Topic successfully added"
        }

        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':topicMst.id])
    }

    @Secured(['ROLE_USER'])
    def aliascreation() {
        TopicMst topicMst
        if(params.topic==null||"".equals(params.topic)){
            flash.error = "Please enter topic"
        } else {
            topicMst =  new TopicMst(topicName: params.topic, syllabus: params.syllabus, grade: params.grade, subject: params.subject, createdBy: springSecurityService.currentUser.username, parentId: params.parentId)
            topicMst.save(failOnError: true)
            flash.message = "Topic alias successfully added"
        }

        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':topicMst.id])
    }

    @Secured(['ROLE_USER'])
    def checkTopicNameExists() {
        TopicMst topicMst = TopicMst.findBySyllabusAndGradeAndSubjectAndTopicName(params.syllabus,params.grade,params.subject,params.topic)
        render(topicMst==null?0:topicMst.id)
    }

    @Secured(['ROLE_USER'])
    def addResource(){
        String actionMethod
        switch (params.useType){
            case "quiz":
                actionMethod ="addQuiz"
                break
            case "link":
                actionMethod = "addlink"
                break
            case "file":
                actionMethod= "addFile"
                break
            case "notes":
                actionMethod= "addFile"
                break
        }

        forward(action: actionMethod)
    }

    @Secured(['ROLE_USER'])
    def addFile(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')
        def resourceDtlInstance = new ResourceDtl()

        if(file.empty) {
            flash.message = "File cannot be empty"
        } else {
            resourceDtlInstance.filename = file.originalFilename
            resourceDtlInstance.resLink = "upload/" +params.topicId+"/" + resourceDtlInstance.filename
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
            resourceDtlInstance.topicId = new Integer(params.topicId)
            resourceDtlInstance.resourceName = params.resourceName

            File uploadDir = new File("upload/" +params.topicId)
            if(!uploadDir.exists()) uploadDir.mkdirs()
            file.transferTo(new File(grailsApplication.config.grails.basedir.path+resourceDtlInstance.resLink))
            resourceDtlInstance.save(failOnError: true)
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }
			userManagementService.addPoints(springSecurityService.currentUser.username,"CP","ADDFILE",resourceDtlInstance.id.toString())
        }

        flash.message = "Notes for"+ params.resourceName+" added"
        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':params.topicId])
    }

    @Secured(['ROLE_USER'])
    def addQuiz(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')
        def msgStr

        WorkbookSettings settings = new WorkbookSettings()
        settings.setSuppressWarnings(true)
        Workbook workbook = Workbook.getWorkbook(file.getInputStream(),settings)
        Sheet sheet = workbook.getSheet(0)

        flash.message = "Document being processed is <b>"+file.originalFilename+"</b><br>"
        // skip first row (row 0) by starting from 1
        flash.message += "Total number of rows is "+sheet.getRows()+"<br>"

        //checks before actual writing
        if("Multiple Choice Questions".equals(params.resourceType))
            msgStr = quizExtractorService.checkMCQ(sheet)
        else if("Fill in the blanks".equals(params.resourceType))
            msgStr = quizExtractorService.checkFillintheblanks(sheet)
        else
            msgStr = quizExtractorService.checkOthers(sheet)

        //writing to db only if spreadsheet passes all checks
        if(msgStr==null || msgStr.equals("")) {
            QuizIdGenerator quizIdGenerator = new QuizIdGenerator()
            quizIdGenerator.save()

            if("Multiple Choice Questions".equals(params.resourceType))
                quizExtractorService.extractMCQ(sheet,quizIdGenerator.id,params.resourceType,params.topicId,springSecurityService.currentUser.username)
            else if("Fill in the blanks".equals(params.resourceType))
                quizExtractorService.extractFillintheblanks(sheet,quizIdGenerator.id,params.resourceType)
            else
                quizExtractorService.extractOthers(sheet,quizIdGenerator.id,params.resourceType)

            def resourceDtlInstance = new ResourceDtl()
            resourceDtlInstance.filename = file.originalFilename
            resourceDtlInstance.resLink = quizIdGenerator.id
            resourceDtlInstance.createdBy = springSecurityService.currentUser.username
            resourceDtlInstance.resType = params.resourceType
            if(params.chapterId!=null&&""!=params.chapterId)
                resourceDtlInstance.chapterId = new Integer(params.chapterId)
            else
                resourceDtlInstance.topicId = new Integer(params.topicId)
            resourceDtlInstance.resourceName = params.resourceName
            resourceDtlInstance.save(failOnError: true)
            if(resourceDtlInstance.sharing==null){
                dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
            }else{
                dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
            }
			def c = ObjectiveMst.countByQuizId(quizIdGenerator.id)
			if(c!=null) {
				userManagementService.addUpdatePoints(springSecurityService.currentUser.username,"CP","ADDQUIZ",resourceDtlInstance.id.toString(),c.toInteger())
			}

            flash.message += "<br><br>Quiz for <b><u>"+params.resourceName+"</u></b> was added successfully! Thanks for your contribution. You can use this quiz and share it with your friends now. In order to share with everybody, please use the <b>Publish</b> option."
        } else {
            flash.message += msgStr
            flash.message += "<br><br><font color=red>Please rectify the above error before uploading the document again.</font>"
        }

        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':params.topicId])
    }

    @Secured(['ROLE_USER'])
    def addlink(){
        Cookie cookie = new Cookie("creationSyllabus",params.syllabus)
        cookie.maxAge = 10000000
        response.addCookie(cookie)
        def resourceDtlInstance = new ResourceDtl()
        resourceDtlInstance.resLink = params.link
        resourceDtlInstance.createdBy = springSecurityService.currentUser.username
        resourceDtlInstance.resType = params.resourceType
        resourceDtlInstance.topicId = new Integer(params.topicId)
        resourceDtlInstance.resourceName = params.resourceName
        resourceDtlInstance.save(failOnError: true)
        if(resourceDtlInstance.sharing==null){
            dataProviderService.resourceCacheUpdate(resourceDtlInstance.id)
        }else{
            dataProviderService.getChapterResourcesForUser(new Long(params.chapterId),springSecurityService.currentUser.username)
        }
		userManagementService.addPoints(springSecurityService.currentUser.username,"CP","ADDLINK",resourceDtlInstance.id.toString())

        flash.message = "Link for "+ params.resourceName+" added"
        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':params.topicId])
    }

    def download(long id) {
        ResourceDtl documentInstance = ResourceDtl.get(id)
        if (documentInstance == null) {
            flash.message = "Document not found."
            redirect(action: 'list')
        } else {
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "AttachmentFilename=\"${documentInstance.filename}\"")
            def file = new File(documentInstance.resLink)
            def fileInputStream = new FileInputStream(file)
            def outputStream = response.getOutputStream()
            byte[] buffer = new byte[4096]
            int len
            while ((len = fileInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len)
            }

            outputStream.flush()
            outputStream.close()
            fileInputStream.close()

			userManagementService.addPoints((springSecurityService.currentUser!=null?springSecurityService.currentUser.username:""),"LP","VIEW",id.toString())
        }
    }

    @Transactional
    def displayChapter(){
        List chapterDetails = ResourceDtl.executeQuery("select t1.resType,t1.resourceName,t1.createdBy,t1.resLink,t2.useType,t1.id from ResourceDtl t1,ResourceType t2 where t1.resType = t2.resourceType and t1.topicId=56")
        [chapterDetails: chapterDetails]
    }

    def contentCreator(){
    }

    @Transactional
    def addUser(){
        String entryController=g.cookie(name: 'siteName');
        String username,password,name,registeredFrom,mobile,email,otpFinished,accessCodes,tempUserName,termsCondition
        String institution="",department="",interests="",otherInterests="",qualification="",studentDiscipline="",country="",userType="",state="",district=""
        Long siteId
        boolean json=false
        boolean newUser=false
        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            json=true
            def jsonObj = request.JSON
            username=jsonObj.username
            tempUserName=jsonObj.username
            email=jsonObj.email!=null?jsonObj.email:username
            password=jsonObj.password
            name=jsonObj.name
            mobile = jsonObj.mobile
            registeredFrom="mobile"
            siteId = new Long(jsonObj.siteId)
            otpFinished=jsonObj.otp_finished
            state=jsonObj.state
            district=jsonObj.district
            accessCodes=jsonObj.accessCode
        } else {
            username=params.username
            tempUserName=params.username
            password=params.password
            name=params.name
            mobile=params.mobile
            registeredFrom="web"
            email=params.email!=null?params.email:username
            institution = params.institution
            department = params.department
            interests = request.getParameterValues("interests")!=null?String.join(",", request.getParameterValues("interests")):""
            otherInterests = params.otherInterests
            qualification = params.qualification
            studentDiscipline = params.studentDiscipline
            country = params.country
            userType = params.userType
            siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
            otpFinished=params.otp_finished
            state=params.state
            district=params.district
            accessCodes=params.accessCode
            termsCondition=params.termsCondition
        }
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        if(sm.id.intValue()==12||sm.id.intValue()==23||sm.id.intValue()==24){
            def json2
            if(password.length()>64){
                json2 = [status: 'failed', message: 'Password too long.']
                render json2 as JSON
                return
            }else if(password.length()<8){
                json2 = [status: 'failed', message: 'Password too short.']
                render json2 as JSON
                return
            }else if(password.indexOf(name)>-1){
                json2 = [status: 'failed', message: 'Password cannot contain your name.']
                render json2 as JSON
                return
            }else if(!password.matches(/^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$/)){
                json2 = [status: 'failed', message: 'Password must contain a upper case character, a lower case character and a number.']
                render json2 as JSON
                return
            }else if(password.matches(/^(?=.*[!@#$%^&*()_+=""''~`{}|:;?<>.-])/)){
                json2 = [status: 'failed', message: 'Password cannot contain any special character.']
                render json2 as JSON
                return
            }
        }
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        def signedUpInstituteId = null
        if(session['fromInstitutePageinstituteId']!=null) signedUpInstituteId = session['fromInstitutePageinstituteId']
        if(!username.startsWith(""+siteId+"_")) username = ""+siteId+"_"+username
        if(session["userdetails"]!=null&&session["userdetails"].username.indexOf("1_cookie_")==0) {
            updateDataSet(session["userdetails"].username, username)
            User user = session["userdetails"]
            user.oldUsername = user.username
            user.username = username
            user.mobile = mobile
            user.email = email
            user.registeredFrom = registeredFrom
            user.signedUpInstituteId = signedUpInstituteId
            user.save(failOnError: true, flush: true)
            session['userdetails'] = user
            springSecurityService.reauthenticate(user.username, user.password)
            def authentication = SecurityContextHolder.context.authentication
            rememberMeServices.loginSuccess(request, response, authentication)
            userManagementService.registerUserLogin(user.username,session.getId())

            def json1 = ['status': "OK"]
            render json1 as JSON
        }
        else {
            User user
            if (username.startsWith("Google")) {
                user = User.findByEmail(email)
            } else {
                user = User.findByUsername(username)
            }
            if (user == null && ((tempUserName.matches('^[0-9]*$') && tempUserName.length() == 10)|| tempUserName.indexOf("@")>-1)) {
                WinGenerator winGenerator = new WinGenerator()
                winGenerator.save(failOnError: true)
                newUser = true
                if (email != null) {
                    if (email.startsWith("Google"))
                        email = email.substring(6)
                    else if (email.startsWith("Facebook"))
                        email = "<EMAIL>"
                }

                user = new User(username: username, password: password, name: name, email: email, registeredFrom: registeredFrom, mobile: mobile, siteId: siteId,
                        institution: institution, department: department, interests: interests, otherInterests: otherInterests, qualification: qualification,
                        studentDiscipline: studentDiscipline, country: country, userType: userType, otpFinished: otpFinished, state: state, district: district,signedUpInstituteId:signedUpInstituteId)
                user.win = winGenerator.id
                if(termsCondition!=null && !"".equals(termsCondition)){
                    user.termsCondition='true'
                    user.tcAcceptedDate=new Date()
                    String ipAddress = utilService.getIPAddressOfClient(request)
                    user.ipAddress=ipAddress
                }
                user.save(failOnError: true, flush: true)
                Role role = Role.findByAuthority("ROLE_USER")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_ADD")
                UserRole.create(user, role, true)
                role = Role.findByAuthority("ROLE_CAN_UPLOAD")
                UserRole.create(user, role, true)
                springSecurityService.reauthenticate(user.username, user.password)
                def authentication = SecurityContextHolder.context.authentication
                rememberMeServices.loginSuccess(request, response, authentication)
                session['userdetails'] = user

                List batchesToBeAdded
                batchesToBeAdded = BatchUserDtlNotRegistered.findAllByEmail(user.username)
                batchesToBeAdded.each { batch ->
                    BatchUserDtl batchUserDtl = new BatchUserDtl(username: user.username, batchId: batch.batchId, createdBy: batch.createdBy, instructor: batch.instructor)
                    batchUserDtl.save(failOnError: true, flush: true)
                }

                dataProviderService.getBooksListForUser(user.username)

                if (siteId.intValue() == 9) {
                    session["sageUserCreated"] = "true"
                }

                if (user.email != null && !user.email.equals("")) {
                    if (userManagementService.validateEmail(user.email, (Integer) session["siteId"])) {
                        try {
                            userManagementService.sendWelcomeEmail(user.email, user.name, (Integer) siteId, siteName, clientName)
                        } catch (Exception e) {
                            println "sending welcome email failed " + e.toString()
                        }
                    }
                }

                if (siteId.intValue() == 24 && accessCodes != null && !"".equals(accessCodes)) {
                    AccessCode accessCode = AccessCode.findByCode(accessCodes)
                    CourseBatchesDtl courseBatchesDtl = CourseBatchesDtl.findByConductedBy(accessCode.instituteId)
                    BatchUserDtl batchUserDtl = new BatchUserDtl(username: user.username, batchId: new Long(courseBatchesDtl.id), createdBy: user.username)
                    batchUserDtl.save(failOnError: true, flush: true)
                    accessCode.username = user.username
                    accessCode.status = "Redeemed"
                    accessCode.dateRedeemed = new Date();
                    accessCode.save(failOnError: true, flush: true)
                    dataProviderService.getBooksListForUser();
                }

                userManagementService.addPoints(user.username, "SP", "REGISTRATION", null)
                userManagementService.updatePoints(user.username, "SP", "INVITATION")
            } else {
                if (springSecurityService.passwordEncoder.isPasswordValid(user.password, password, null)) {
                    if ("Do not allow".equals(userLogService.checkNumberOfSimultaneousUsers(user.username))) {
                        SecurityContextHolder.clearContext()
                        Cookie cookie = new Cookie("SimulError", "Fail")
                        cookie.path = "/"
                        WebUtils.retrieveGrailsWebRequest().getCurrentResponse().addCookie(cookie)
                        flash.message = "Sorry, you have already signed in from  [" + grailsApplication.config.grails.appServer.maximumSessions + "] devices. To login here, please sign out from other device!"
                        redirect(controller: siteName, view: 'store', params: ['loginFailed': 'true'])
                        return
                    } else {
                        springSecurityService.reauthenticate(user.username, user.password)
                        session['userdetails'] = user
                    }
                } else {
                    redirect(controller: "Security", action: "loginfailedmanager")
                    return
                }
            }
            if (sm.sageOnly!="true") json = true; //for Wonderslate and Arihant site

            if (json) {
                def json1 = ['status': "OK",username:user.username]
                render json1 as JSON
            }else if("quiz".equals(params.forceRegisterMode)){
                println("the newly created username is "+user.username)
                def json1 = ['status': "OK",username:user.username]
                render json1 as JSON
            } else {
                if ("9".equals("" + session["siteId"])) {
                    if ("student".equals(user.userType)) {
                        redirect([uri: '/sage/studentResources?siteName=sage&isbn=' + session.getAttribute("isbn")])
                    } else {
                        redirect([uri: '/sage/instructorResources?siteName=sage&isbn=' + session.getAttribute("isbn")])
                    }
                } else {
                    if (newUser) {
                        //this is the case for weblogin via google and the user is new
                        if (username.startsWith("Google")) {
                            def json1 = ['status': "new"]
                            render json1 as JSON
                        } else {
                            if ("12".equals("" + session["siteId"]) || "23".equals("" + session["siteId"]) || "24".equals("" + session["siteId"])) {
                                session["successModal"] = "true"
                                redirect([uri: '/' + entryController + '/index'])
                            } else {

                                if ("1".equals("" + session["siteId"]) || "5".equals("" + session["siteId"]))
                                    redirect(controller: siteName, action: 'store', params: ['mode': 'browse'])
                                else
                                    redirect([uri: '/library'])

                            }
                        }
                    } else {
                        def json1 = ['status': "OK"]
                        render json1 as JSON
                    }
                }
            }
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def updateUserInformation() {
        User user = User.findByUsername(springSecurityService.currentUser.username)
        user.address1 = params.address1
        user.city = params.city
        user.state = params.state
        user.district = params.district
        user.twitter = params.twitter
        user.save(failOnError: true,flush: true)
        session['userdetails'] = user
    }

    def userCreationEmail(){
		[name:'test']
    }

    @Secured(['ROLE_APPROVER'])
    def approveContent(){
        if("approve".equals(params.mode)){
            redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':params.topicId,'approveMode':'true'])
        }
    }

    @Secured(['ROLE_APPROVER'])
    def getItemsForApproval(){
        String sql = "select rd.id, rd.topicId, rd.resType, rd.resLink, rd.resourceName, rd.dateCreated, "+
                "tm.topicName, tm.syllabus, tm.grade, tm.subject,um.name from ResourceDtl rd, TopicMst tm, User um "+
                "where tm.id=rd.topicId and rd.sharing is null and um.username=rd.createdBy  "+
                "order by  rd.dateCreated desc"

		List approvalItems =  ResourceDtl.executeQuery(sql)
        List jsonChapter = approvalItems.collect {comp ->
            return [ id: comp[0], topicId: comp[1], resType: comp[2],  resLink: comp[3], resName: comp[4], dateCreated: comp[5], topicName: comp[6], syllabus: comp[7], grade: comp[8], subject: comp[9], name:comp[10] ]
        }

        def json =[
			'results': jsonChapter,
			'status': jsonChapter ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    @Secured(['ROLE_APPROVER']) @Transactional
    def approvalChapterDetails(){
        List chapterDetails  = ResourceDtl.findAllByTopicIdAndSharing(params.topicId,null,[sort:"resType"])
        List jsonChapter = chapterDetails.collect{comp ->
            return [id: comp.id, resType: comp.resType,  resLink: comp.resLink, resName: comp.resourceName,
                    canDelete:(springSecurityService.currentUser!=null)? userManagementService.canDelete(springSecurityService.currentUser.username, comp.createdBy):false ,
                    canEdit:(springSecurityService.currentUser!=null)? userManagementService.canEdit(springSecurityService.currentUser.username, comp.createdBy):false,
                    canApprove:true]
        }

        def json =[
			'results': jsonChapter,
			'status': jsonChapter ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    def testPage(){
    }

    @Secured(['ROLE_USER']) @Transactional
    def removeItem(){
        String sql = "update ResourceDtl set sharing='deleted' where id="+params.id
        ResourceDtl.executeUpdate(sql)

        //AA: after this.. we need to add logic to see if this was the last active item for the chapter. If so we have to change the status of the chapter/topic
        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':params.topicId,'approveMode': params.approveMode])
    }

    @Secured(['ROLE_USER']) @Transactional
    def approveItem(){
        String sql = "update ResourceDtl set sharing='public' where id="+params.id
        ResourceDtl.executeUpdate(sql)

        sql = " update TopicMst set canBeDisplayed='true' where id="+params.topicId
        TopicMst.executeUpdate(sql)

        redirect (controller: 'funlearn' , action: 'topic', params: ['topicId':params.topicId,'approveMode': params.approveMode])
    }

    def register(){}

    def checkUserNameExists(){
        String username=""
        boolean json=false
        Integer siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                json=true
                def jsonObj = request.JSON
                username=jsonObj.username
                siteId = new Integer(jsonObj.siteId)
            }
            else username=params.username
        }

        User user = User.findByEmailAndSiteId(username.startsWith("Google")?username.substring(6):username,siteId)

        if(json){
            def json1 =['status': user==null ? "0" : "1",
                        'username': user==null ? "" : user.username]
            render json1 as JSON
        } else
			render(user==null?0:1)
    }

    def checkMobileUserNameExists(){
        String username=""
        boolean json=false

            if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
                json=true
                def jsonObj = request.JSON
                username=jsonObj.username
            }
            else{
                username=params.username
            }

        User user = User.findByUsername(username)

        if(json){
            def json1 =['status': user==null ? "0" : "1",
                        'username': user==null ? "" : user.username]
            render json1 as JSON
        } else
            render(user==null?0:1)
    }

    def socialLogin(){
        String username=""
        boolean json=false
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                json=true
                def jsonObj = request.JSON
                username=jsonObj.username
            }
            else username=params.username
        }

        User user = User.findByEmail(username.startsWith("Google")?username.substring(6):username)
        if(user==null){

        }

        if(json){
            def json1 =['status': user==null ? "0" : "1",
                        'username': user==null ? "" : user.username]
            render json1 as JSON
        } else
            render(user==null?0:1)
    }

    def checkMobileExists(){
        String mobile
        boolean json=false
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                json=true
                def jsonObj = request.JSON
                mobile=jsonObj.mobile
            } else
                mobile=params.mobile
        }

        User user = User.findByMobile(mobile)

        if(json){
            def json1 =['status': user==null ? "0" : "1"]
            render json1 as JSON
        } else
			render(user==null?0:1)
    }

    @Secured(['ROLE_USER']) @Transactional
    def loggedactivities(){
    }

    @Secured(['ROLE_USER'])
    def welcomeModal(){
    }

    @Secured(['ROLE_USER'])
    def basicProfileUpdate(){
        User  user = User.findByUsername(springSecurityService.currentUser.username)
        user.sex = params.sex
        user.teacher = params.teacher
        user.student = params.student
        user.parent = params.parent
        user.syllabus = params.syllabus
        user.classStudying = params.grade
        user.qualification = params.qualification
        user.city = params.city
        user.registeredFrom = params.syllabusType
        user.save(failOnError: true,flush: true)
        session['userdetails'] = user

        if("start".equals(params.actionType))
        redirect (controller: 'funlearn',action: 'home')
        else if("tour".equals(params.actionType))
            redirect (controller: 'funlearn',action: 'tour')
        else if("profile".equals(params.actionType))
            redirect (controller: 'creation',action: 'editProfile')
    }

    @Secured(['ROLE_USER'])
    def editProfile(){
        if("update".equals(params.mode)){
            User user = User.findByUsername(springSecurityService.currentUser.username)
            user.name = params.name
            user.city = params.city
            user.country = params.country
            user.profession = params.profession
            user.qualification = params.qualification
            user.syllabus = params.syllabus
            user.school = params.school
            user.parent = params.parent
            user.student = params.student
            user.teacher = params.teacher
            user.classStudying = params.classStudying
            user.interests = params.interests
            user.introduction = params.introduction
            user.webpage = params.webpage
            user.facebook = params.facebook
            user.linkedin = params.linkedin
            user.twitter = params.twitter
            user.registeredFrom = params.syllabusType
            user.save(failOnError: true,flush: true)
            session['userdetails'] = user
            redirect (controller: 'funlearn',action: 'home')
        }

        [title:'Edit Profile']
    }

    @Secured(['ROLE_USER'])
    def editUserProfile(){
        String name,mobile
        boolean json=false

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                json=true
                def jsonObj = request.JSON
                name=jsonObj.name
                mobile = jsonObj.mobile
            } else {
                name=params.name
                mobile=params.mobile
            }
        }

        User user = User.findByUsername(springSecurityService.currentUser.username)
        user.name = name
        user.mobile = mobile
        user.save(failOnError: true,flush: true)
        session['userdetails'] = user

        if(json){
            render(contentType: 'text/json') {[
                'status': "OK"
            ]}
        } else
			redirect (controller: 'wonderpublish' , view: 'mybooks')
    }

    @Secured(['ROLE_USER'])
    def uploadprofile(){
        final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multiRequest.getFile("file");
//        def file = request.getFile('file')

        if(file.empty) {
            flash.message = "File cannot be empty"
        } else {
            if("user".equals(params.type)){
                User user = User.findByUsername(springSecurityService.currentUser.username)
                File uploadDir = new File("upload/user/"+user.id)
                if(!uploadDir.exists()) uploadDir.mkdirs()

                //creating directory to process images
                File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
                if(!uploadDir1.exists()) uploadDir1.mkdirs()
                String filename=file.originalFilename
                filename=filename.replaceAll("\\s+","")

				BufferedImage image = ImageIO.read(file.getInputStream())

				ByteArrayOutputStream baos = new ByteArrayOutputStream()
				ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
				baos.flush()
				byte[] scaledImageInByte = baos.toByteArray()
				baos.close()

				baos = new ByteArrayOutputStream()
				ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
				baos.flush()
				byte[] scaledImageInByte1 = baos.toByteArray()
				baos.close()

				baos = new ByteArrayOutputStream()
				ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
				baos.flush()
				byte[] scaledImageInByte2 = baos.toByteArray()
				baos.close()

                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

                //saving original image finally
                file.transferTo(new File(uploadDir.absolutePath+"/"+file.originalFilename))

                user.profilepic = file.originalFilename
                user.save(failOnError: true,flush: true)
                session["userdetails"] = user
                redirect(controller: params.sourceController, action:params.source)
            } else if("group".equals(params.type)){
                Groups group = Groups.findById(new Integer(params.groupId))
                File uploadDir = new File("upload/group/"+group.id)
                if(!uploadDir.exists()) uploadDir.mkdirs()
                String filename=file.originalFilename
                filename=filename.replaceAll("\\s+","")
                //creating directory to process images
                File uploadDir1 = new File(uploadDir.absolutePath+"/processed")
                if(!uploadDir1.exists()) uploadDir1.mkdirs()

				BufferedImage image = ImageIO.read(file.getInputStream())

				ByteArrayOutputStream baos = new ByteArrayOutputStream()
				ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 125, 125, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
				baos.flush()
				byte[] scaledImageInByte = baos.toByteArray()
				baos.close()

				baos = new ByteArrayOutputStream()
				ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 252, 343, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
				baos.flush()
				byte[] scaledImageInByte1 = baos.toByteArray()
				baos.close()

				baos = new ByteArrayOutputStream()
				ImageIO.write(Scalr.resize(image,Scalr.Method.QUALITY, Scalr.Mode.FIT_TO_WIDTH, 50, 50, Scalr.OP_ANTIALIAS), filename.substring(filename.indexOf(".")+1), baos)
				baos.flush()
				byte[] scaledImageInByte2 = baos.toByteArray()
				baos.close()

                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_thumbnail'+filename.substring(filename.indexOf("."))), scaledImageInByte)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_passport'+filename.substring(filename.indexOf("."))), scaledImageInByte1)
                FileUtils.writeByteArrayToFile(new File(uploadDir1.absolutePath+"/"+filename.substring(0,filename.indexOf("."))+'_icon'+filename.substring(filename.indexOf("."))), scaledImageInByte2)

                //saving original image finally
                file.transferTo(new File(uploadDir.absolutePath+"/"+file.originalFilename))

                group.profilepic = file.originalFilename
                group.save(failOnError: true,flush: true)
                redirect(controller: "funlearn", action:"groupdtl", params: [groupId:group.id, displayName: group.name])
            }
        }
    }

    @Secured(['ROLE_USER']) @Transactional
    def publish(){
        ResourceDtl resourceDtl = ResourceDtl.findById(new Integer(params.id))

        //similar logic to be used in all db updates to make sure.. there is not hacking
        if(userManagementService.canPublish(springSecurityService.currentUser.username,resourceDtl.createdBy,resourceDtl.sharing)) {
            def sharing="AwaitingApproval"
            if("Yes".equals(((User)session['userdetails']).canPublish)) sharing = "public"
            resourceDtl.sharing=sharing
            resourceDtl.save(failOnError: true,flush: true)

			userManagementService.addOncePoints(springSecurityService.currentUser.username,"SP","PUBLISH",params.id)

            def json = [
				'id': params.id,
                'status' :  sharing
            ]

            render json as JSON
        }
    }

    @Transactional
    def generateOTP(){
        String email, mobile, name,userCheck,resend,entryController,logo=null
        boolean checkPassed = true,userExist=true
        Long siteId = null
        String appCode=""
        email = params.email
        mobile = params.mobile
        name = params.name
        appCode=params.appCode
        resend = params.resend
        userCheck = params.userCheck
        entryController = session["entryController"]
        logo = session["logo"]
        siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                email=jsonObj.email
                mobile=jsonObj.mobile
                name=jsonObj.name
                appCode=jsonObj.appCode
                siteId = new Long(jsonObj.siteId)
                resend = jsonObj.resend
                userCheck =jsonObj.userCheck
                entryController = jsonObj.entryController
                logo = jsonObj.logo
            }
        }

        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName

        def otp
        if(email!=null && !email.equals("")) {
            if("true".equals(userCheck) && !"web".equals(params.source)) {
                if(User.findByUsername(""+siteId+"_"+email)==null) userExist=false
            }
            if(userManagementService.validateEmail(email,(Integer)siteId) && userExist) {
                otp = creationService.generateOTP(""+email, sm.id)
                try {
                    userManagementService.sendOtpEmail(name, email, (Integer)siteId, otp+"",siteName,clientName,entryController,logo)
                } catch (Exception e){
                    checkPassed = false
                    println "Exception in sending OTP mail "+e.toString()
                }
            }
        }

        if(mobile!=null && !mobile.equals("")) {
            if("true".equals(userCheck) && !"web".equals(params.source)) {
                if(User.findByUsername(""+siteId+"_"+mobile)==null) userExist=false
            }
            if(userExist) {
                otp = creationService.generateOTP(""+mobile, sm.id)
                def sms_txt_acc
                sms_txt_acc = sm.smsSenderName
                def message
                if(sm.prepjoySite=="true") {
                    message = "Use ${otp} as one time password (OTP) to activate your ${sms_txt_acc} account.${appCode != null ? "\n" + appCode : ''} -- Wonderslate Technologies Pvt Ltd"
                }else if(sm.id==38){
                    message = "Use ${otp} as a one-time password (OTP) to activate your ${sms_txt_acc} account.${appCode != null ? "\n" + appCode : ''}"
                }else{
                    message = "Use ${otp} as one time password (OTP) to activate your ${sms_txt_acc} account. -- Wonderslate Technologies Pvt Ltd"
               }
                utilService.sendSMS(siteId,message,mobile,resend)
            }

        }

        def json = ['email':email,'mobile':mobile,'status':checkPassed?"OK":"Failed",'userExist':userExist]
        render json as JSON
    }

    @Transactional
    def callOTP(){
        String mobile = params.mobile
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                mobile=jsonObj.mobile

            }
        }
        AuthenticationOtp authOtp = AuthenticationOtp.findByContact(mobile)
        if(authOtp!=null) {
            RestBuilder rest = new RestBuilder()
            def resp = rest.post("http://117.215.67.39:8888/ConvoqueAPI/placeACall.jsp?src=" + mobile + "&dst=" + mobile + "&type=g0&refID=" + authOtp.otp)

            def json = [results: resp.text]
            render json as JSON
        }else{
            def json = [results: "OTP not found"]
            render json as JSON
        }

    }

    @Transactional
    def checkOTP() {
        String email, mobile, email_otp, mobile_otp

        boolean checkPassed = true
        String statusStr = "Fail"
        boolean userExists=false;
        boolean allowLogin=true
        User user;
        Integer siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)

        String authenticationTokenString;
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                email=jsonObj.email
                mobile=jsonObj.mobile
                email_otp=jsonObj.email_otp
                mobile_otp=jsonObj.mobile_otp
                siteId = new Long(jsonObj.siteId)
            } else {
                email = params.email
                mobile = params.mobile
                email_otp = params.email_otp
                mobile_otp = params.mobile_otp
            }
        }


        AuthenticationOtp authOtp

        if (mobile!=null && !mobile.equals("")) {
            authOtp = AuthenticationOtp.findAllByContactAndSiteId(mobile,siteId,[sort:"dateCreated",order:"desc"])[0]
           if(authOtp==null || !mobile_otp.equals(authOtp.otp)) {
                checkPassed = false
                statusStr = "M"+statusStr
            }else{
                creationService.deleteOTPForUser(""+mobile,siteId)
            }
        }

        if (checkPassed && email!=null && !email.equals("")) {
            authOtp = AuthenticationOtp.findAllByContactAndSiteId(email,siteId,[sort:"dateCreated",order:"desc"])[0]
            if(authOtp==null || !email_otp.equals(authOtp.otp)) {
                checkPassed = false
                statusStr = statusStr+"E"
            }else{
                creationService.deleteOTPForUser(""+email,siteId)
            }
        }
       if(checkPassed) {
           //to the see if the user already exists
           String siteName = dataProviderService.getSiteMst(siteId).siteName
           if (mobile != null && !mobile.equals("")) {
               if("eutkarsh".equals(siteName))
               user = User.findByUsername(mobile)
               else user = User.findByUsername(""+siteId+"_"+mobile)
           } else {
               if("eutkarsh".equals(siteName))
               user = User.findByUsername(email)
               else user = User.findByUsername(""+siteId+"_"+email)
               if (user == null) {
                   user = User.findByUsername("Google" + email)
               }
           }


           if (user != null) {
               userExists = true
               user.otpFinished = "true"
               user.save(failOnError: true, flush: true)
               session['userdetails'] = user
               //migrating WS google signed in users to normal users
               if (grailsApplication.config.grails.appServer.default == "books" && user != null && user.username.startsWith("Google")) {
                   def jsonReturn = migrateGoogleUserWithMerge(user)
                   user = User.findByUsername(user.email)

               }

               if (params.email == null && params.mobile == null) {
                   //app login
                   UUID gfg = UUID.randomUUID();
                   authenticationTokenString = gfg.toString()
                   dataNotificationService.sendLogoutNotificationToUser(user.username, siteId, authenticationTokenString)

                   //force logout
                   List appTokens = AuthenticationToken.findAllByUsername(user.username)
                   appTokens.each { appToken ->
                       appToken.delete(flush: true)
                   }

                   //remove the device Ids
                   List deviceIds = DeviceInformation.findAllByUsername(user.username)
                   deviceIds.each { device ->
                       device.delete(flush: true)
                   }

                   AuthenticationToken authenticationToken = new AuthenticationToken(username: user.username, token: authenticationTokenString)
                   authenticationToken.save(failOnError: true, flush: true)
               } else {
                   if (allowLogin) {
                       if (params.email != null || params.mobile != null) {
                           //web user .. log them in
                           springSecurityService.reauthenticate(user.username)
                           def authentication = SecurityContextHolder.context.authentication
                           rememberMeServices.loginSuccess(request, response, authentication)
                           userManagementService.registerUserLogin(user.username,session.getId())
                           session["NumberExceeded"]="false"
                           UserLog log = new UserLog(username: user.username, action: "login")
                           log.save(failOnError: true,flush: true)

                       }
                    }
                }
            }
        }

        def json = ['status':checkPassed?"OK":statusStr, 'userExists':userExists,'allowLogin':allowLogin,'token':authenticationTokenString,username:user!=null?user.username:null]
        render json as JSON
    }

    @Transactional
    def forgottenPassword(){
        String email=null
        Long siteId=null

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                email=jsonObj.email
                siteId = new Long(jsonObj.siteId)
            } else {
                email = params.email
                siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
            }
        }

        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName

        if(!"eutkarsh".equals(sm.siteName)) email=""+siteId+"_"+email
        User user = User.findByUsername(email)
        def json
        def status

        if(user!=null && !user.username.startsWith("Google")) {
            if(userManagementService.validateEmail(user.email,(Integer)session["siteId"])) {
                List<ForgotPassword> forgotPasswordList = ForgotPassword.findAllByUserId(user.id)
                if(forgotPasswordList!=null && forgotPasswordList.size()>0){
                    for(int i =0;i<forgotPasswordList.size(); i++){
                        String sql  = "update ForgotPassword set status='expired' where id="+forgotPasswordList[i].id
                        ForgotPassword.executeUpdate(sql)
                    }
                }
                def generator = { String alphabet, int n ->
                    new Random().with {
                        (1..n).collect { alphabet[ nextInt( alphabet.length() ) ] }.join()
                    }
                }

                def token = generator( (('A'..'Z')+('0'..'9')).join(), 30 )
                ForgotPassword forgotPassword = new ForgotPassword(userId: user.id,token: token, dateCreated: new Date())
                forgotPassword.save(failOnError: true,flush: true)
                String url = (request.getRequestURL()).toString()
                String serverUrl = url.substring(0,url.indexOf('/',9))
                if(userManagementService.validateEmail(user.email,(Integer)session["siteId"])) {
                try {
                    userManagementService.sendPwResetEmail(user.email, user.name, (Integer)siteId,
                            serverUrl+"/creation/resetPassword?token="+token+"&siteName="+session['entryController'], siteName, clientName)
                } catch (Exception e){
                    println "Exception in sending forgotten password mail "+e.toString()
                }
                }

                status = "OK"
            }
        } else if(user!=null && user.username.startsWith("Google")) {
            status = "Google"
        } else {
            status = "Fail"
        }

        json = ['email': email,'status':  status]
        render json as JSON
    }

    def submitUserForm(){
        String email
        String name
        String query
        String subject
        String captcha

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                email=jsonObj.email
                name=jsonObj.name
                query=jsonObj.query
                subject=jsonObj.subject
                captcha=jsonObj.captcha
            } else {
                email = params.email
                name = params.name
                query = params.query
                subject = params.subject
                captcha = params.captcha
            }
        }

        def json
        boolean captchaValid

        try {
            captchaValid = simpleCaptchaService.validateCaptcha(captcha)

            if(captchaValid)  {
                if(userManagementService.validateEmail(email,(Integer)session["siteId"])) {
                    userManagementService.sendUserFormEmail(name, email, query, subject)
                    json = ['status' :  "OK"]
                }
            } else throw new Exception()
        } catch (Exception e){
            json = ['status' :  "Fail"]
            println "Exception in sending user form submit mail "+e.toString()
        }

        render json as JSON
    }



    @Transactional
    def resetPassword(){
        ForgotPassword forgotPassword = ForgotPassword.findByToken(params.token)
        String entryController=g.cookie(name: 'siteName');

        if(forgotPassword!=null){
            if((12==getSiteId(request) || 24==getSiteId(request) || 23==getSiteId(request)) && forgotPassword.status=='expired'){
                redirect([uri: '/' + session['entryController'] + '/index?fplink=expired'])
                return
            }
            if(((forgotPassword.dateCreated.getTime()+new Long(86400000))<new Date().getTime()) && (12==getSiteId(request) || 24==getSiteId(request) || 23==getSiteId(request))){
                redirect([uri: '/' + entryController + '/index?fplink=expired'])
                return
            }
            if("submit".equals(params.mode)) {
                User user = User.findById(forgotPassword.userId)
                String password = springSecurityService.encodePassword(params.password)
                user.password = password
                user.save(failOnError: true, flush: true)
                forgotPassword.delete(flush: true)
                flash.message = "Password successfully changed. Please sign in using your new password."
                String sql  = "update ForgotPassword set status='expired' where id="+forgotPassword.id
                ForgotPassword.executeUpdate(sql)
                if(session['entryController']=="sage") {
                    def json = ['status' :  "OK"]
                    render json as JSON
                } else {

                    if("12".equals(""+session["siteId"])||"23".equals(""+session["siteId"])||"24".equals(""+session["siteId"])){
                        redirect([uri: '/' + entryController + '/index'])
                    }
                    else{
                        redirect([uri: '/' + entryController + '/store?showSignIn=true'])
                    }
                }
            }

        } else {
            redirect([uri: '/' + entryController + '/index?fplink=expired'])
        }
        [hideBanner: true]
    }


    @Secured(['ROLE_USER'])
    def inviteFriend(){
        def json

        if(userManagementService.validateEmail(params.email,(Integer)session["siteId"])) {
            mailManagementService.sendInvite(springSecurityService.currentUser.username,params.email,((User)session['userdetails']).name,params.friendname,params.message)
            userManagementService.addPoints(springSecurityService.currentUser.username,"SP","INVITATION",params.email)
            json = ['status' :  "OK"]
        } else {
            json = ['status' :  "Fail"]
        }

        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getUserOrdersList(){

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsshop')
        def sql1 = new Sql(dataSource)
        String sql  =  " SELECT po.id, bm.title, po.date_created, bm.cover_image, bm.id bookId,po.id po_no,po.book_type,po.status" +
                " FROM purchase_order po,books_mst bm where  bm.id=po.item_code and " +
                " po.username='"+springSecurityService.currentUser.username+"' "+
               " order by date_created desc "

        sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        String bookIds = ""
        Date expiryDate


        List orders = results.collect {comp ->
            List dispatchDetails
            bookIds +=comp.bookId+","
                 if("eBook".equals(comp.book_type)||"testseries".equals(comp.book_type)||"upgrade".equals(comp.book_type)||"bookGPT".equals(comp.book_type)||"recharge".equals(comp.book_type)){
                BooksPermission booksPermission = BooksPermission.findByUsernameAndBookId(springSecurityService.currentUser.username,(Integer)comp.bookId)
                if(booksPermission!=null) expiryDate = booksPermission.expiryDate
            }else{
                sql  =  "SELECT delivery_type,partner_details,tracking_code,tracking_link,date_created from dispatch_details where po_no="+comp.id
                sql1 = new Sql(dataSource)
                dispatchDetails = sql1.rows(sql)
            }
            PurchaseOrder purchaseOrder = dataProviderService.getPurchaseOrder(new Long(comp.po_no))

                return [purchaseId: purchaseOrder.sequencePo!=null?"UT"+purchaseOrder.sequencePo:purchaseOrder.id,
                        title: comp.title, currency: purchaseOrder.currency,  amount: purchaseOrder.amount,
                        orderedDate: purchaseOrder.dateCreated,
                        coverImage: comp.cover_image, bookId: comp.bookId, paymentId: purchaseOrder.paymentId,discountAmount:purchaseOrder.discountAmount!=null?purchaseOrder.discountAmount:0,
                        poFor: purchaseOrder.poFor,noOfChapters: "0",
                        expiryDate: expiryDate, status:comp.status,
                        bookType:comp.book_type,dispatchDetails:dispatchDetails]

        }
//
        def json =[
                'results': orders,
                'status': orders ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    def invoice(){

    }

    def ccavRequestHandler() {
        Random generator = new Random()
        def nextPo = generator.nextInt(900000)+100000
        render(view: "ccavRequestHandler", model: [nextPo: nextPo])
    }

    def ccavResponseHandler() {
		String workingKey = "9E74748742AAB8342432FCF15E225793"	//32 Bit Alphanumeric Working Key should be entered here so that data can be decrypted.
		String encResp= params.encResp
		AesCryptUtil aesUtil=new AesCryptUtil(workingKey)
		String decResp = aesUtil.decrypt(encResp)
		StringTokenizer tokenizer = new StringTokenizer(decResp, "&")
		Hashtable hs=new Hashtable()
		String pair, pname, pvalue
		while (tokenizer.hasMoreTokens()) {
			pair = (String)tokenizer.nextToken()
			if(pair!=null) {
				StringTokenizer strTok=new StringTokenizer(pair, "=")
                pvalue=""
				if(strTok.hasMoreTokens()) {
					pname=(String)strTok.nextToken()
					if(strTok.hasMoreTokens())
						pvalue=(String)strTok.nextToken()
					hs.put(pname, URLDecoder.decode(pvalue))
                    if(pvalue!=null && !pvalue.trim().equals("")) {
                        println pname + "=" + URLDecoder.decode(pvalue)
                    }
				}
			}
		}
    }

    def wsinvoice(){}

    def authordetails(){}
    def userCreationEmailEvidya(){}
    def userCreationEmailEtexts(){}
    def resetPasswordEmailEvidya(){}
    def resetPasswordEmailEtexts(){}
    def userCreationEmailEbouquet(){}

    def mobileLogin(){
         forward controller: "api", action: "login", params: [username: "<EMAIL>", password: "password"]
    }

    @Transactional
    def generateUmOTP(){
        String mobile
        String appCode=""
        boolean checkPassed = false
        Long siteId=null
        String encRypt=null

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                mobile=jsonObj.mobile
                appCode=jsonObj.appCode
                siteId = new Long(jsonObj.siteId)
            } else {
                mobile = params.mobile
                appCode=params.appCode
                siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
            }
        }

        def json
        //checking if an user exists with mobile username
        if(mobile!=null && !mobile.equals("")) {
            def user = User.findByUsername(mobile)

            if(user!=null) {
                json = ['mobile':mobile,'status':"Failed",'mCode':"User exists with mobile username"]
                render json as JSON
                return
            }else{
                println("doesn't exist")
            }
        }

        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName

        def otp
        Random generator = new Random()
        AuthenticationOtp authOtp

        if(mobile!=null && !mobile.equals("")) {
            AuthenticationOtp.executeUpdate("delete from AuthenticationOtp where contact='"+mobile+"'")

            otp = generator.nextInt(900000)+100000

            authOtp = new AuthenticationOtp(contact:mobile, otp:otp+"")
            authOtp.save(failOnError: true,flush: true)

            def  sms_txt_acc
            sms_txt_acc = sm.smsSenderName

                def message = "<#>Use ${otp} as one time password (OTP) to activate your ${sms_txt_acc} account.${appCode!=null?"\n"+appCode:''}"
                utilService.sendSMS(siteId,message,mobile)

                encRypt = (new AesCryptUtil(grailsApplication.config.grails.aescrypt.workingKey)).encrypt(siteName);
        }

        json = ['mobile':mobile,'status':checkPassed?"OK":"Failed",'mCode':encRypt]
        render json as JSON
    }

    @Transactional
    def checkUmOTP() {
        String username=null, mobile=null, mobile_otp=""
        boolean checkPassed = false

        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                username=jsonObj.username
                mobile=jsonObj.mobile
                mobile_otp=jsonObj.mobile_otp
            } else {
                username = params.username
                mobile = params.mobile
                mobile_otp = params.mobile_otp
            }
        }

        if (mobile!=null && mobile!="" && username!=null && username!="") {
            AuthenticationOtp authOtp = AuthenticationOtp.findByContact(mobile)

            if(authOtp!=null && mobile_otp==authOtp.otp) {
                checkPassed = true
            }
        }

        if(checkPassed) {
            def json  = migrateUser(mobile,username)
            render json as JSON
        } else {
            def json = ['status':checkPassed?'OK':'Fail']
            render json as JSON
        }
    }

    @Transactional
    def updateData(String tableName, String columnName, String oldValue, String newValue,String datasourceString) {
       def dataSource = grailsApplication.mainContext.getBean(datasourceString)
        new Sql(dataSource).execute("update "+tableName+" set "+columnName+"='"+newValue+"' where "+columnName+"='"+oldValue+"'")
    }


    @Transactional
    def updateDataSet(String oldLogin, String newLogin) {
        updateData("wsuser.annotator_mst","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsshop.purchase_order","username",oldLogin,newLogin,"dataSource_wsshop")
        updateData("resource_group_dtl","username",oldLogin,newLogin,"dataSource")
        updateData("answers","username",oldLogin,newLogin,"dataSource")
        updateData("wscomm.questions","username",oldLogin,newLogin,"dataSource_wscomm")
        updateData("wsuser.batch_user_dtl","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("forms_mst","username",oldLogin,newLogin,"dataSource")
        updateData("wsuser.institute_user_dtl","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.app_version","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wslog.books_view_dtl","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("wscomm.device_information","username",oldLogin,newLogin,"dataSource_wscomm")
        updateData("wslog.key_value_recorder","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("wslog.quiz_issues","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("wslog.quizrecorder","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("wslog.resource_view","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("search","username",oldLogin,newLogin,"dataSource")
        updateData("wsuser.user_chapter_dtl","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.user_resource_dtl","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsshop.book_rating_reviews","username",oldLogin,newLogin,"dataSource_wsshop")

        updateData("wsuser.groups_mst","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_members_dtl","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_comments_reply_dtl","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_post_comments_dtl","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_post_dtl","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_posts_likes_dtl","liked_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_posts_spam_dtl","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_request_dtl","requested_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_spam_dtl","reported_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_user_spam_dtl","reported_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.groups_user_spam_dtl","username",oldLogin,newLogin,"dataSource_wsuser")



        if(!(newLogin.startsWith("Switched_") && newLogin.contains(oldLogin))) {
            updateData("wsuser.books_permission", "username", oldLogin, newLogin,"dataSource_wsuser")
        }

        updateData("wslog.chapter_access","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("wsuser.authentication_token","username",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wslog.points_dtl","username",oldLogin,newLogin,"dataSource_wslog")
        updateData("wslog.user_log","username",oldLogin,newLogin,"dataSource_wslog")

        updateData("books_mst","created_by",oldLogin,newLogin,"dataSource")
        updateData("books_mst","created_by",oldLogin,newLogin,"dataSource_wsshop")
        updateData("books_mst","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("resource_dtl","created_by",oldLogin,newLogin,"dataSource")
        updateData("wslog.tests_mst","created_by",oldLogin,newLogin,"dataSource_wslog")
        updateData("wsuser.tests_shared","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("topic_mst","created_by",oldLogin,newLogin,"dataSource")
        updateData("wsuser.batch_assignments_dtl","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.batch_user_dtl","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.batch_user_dtl_not_registered","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wsuser.institute_ip_address","created_by",oldLogin,newLogin,"dataSource_wsuser")
        updateData("wscomm.notification_dtl","created_by",oldLogin,newLogin,"dataSource_wscomm")
        updateData("wsshop.publishers","created_by",oldLogin,newLogin,"dataSource_wsshop")
        updateData("wsshop.syllabus_grade_dtl","created_by",oldLogin,newLogin,"dataSource_wsshop")
        updateData("wsuser.user_time_log","username",oldLogin,newLogin,"dataSource_wsuser")
    }

    def migrateUser(mobile, username) {
        String statusStr="Not migrated"

        if(mobile!=null && !mobile.equals("")) {
            //checking if an user exists with mobile username
            User user = User.findByUsername(mobile)

            if(user!=null) {
                json = ['status':'Fail','condition':'User exists with mobile username']
                render json as JSON
                return
            }

            user = User.findByUsername(username)

            //if user exists and not yet migrated
            if(user!=null && (user.oldUsername==null || user.oldUsername=="")) {
                user.oldUsername = user.username
                user.username = mobile
                user.mobile = mobile
                user.otpFinished = "true"
                user.save(failOnError: true,flush: true)
                session['userdetails'] = user

                updateDataSet(user.oldUsername,mobile)

                if(session["userDetails"]!=null){
                    springSecurityService.reauthenticate(user.username, user.password)
                    session['userdetails'] = user
                    session["NumberExceeded"]="false"
                }

                statusStr="User migrated"
            } else if(user==null) {
                statusStr="User already migrated or does not exist"
            }
        } else {
            statusStr="Mobile number not valid"
        }

        return ['status':statusStr=='User migrated'?'OK':'Fail', 'condition':statusStr]
    }

    @Transactional
    def switchUser() {
        String username=null, mobile=null, statusStr="Not switched"

        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            username=jsonObj.username
            mobile=jsonObj.mobile
        } else {
            username = params.username
            mobile = params.mobile
        }

        User user, mobileUser
        def json

        if(mobile!=null && !mobile.equals("")) {
            mobileUser = User.findByUsername(mobile)
            if (mobileUser != null && mobileUser.oldUsername != null) {
                statusStr="Mobile number already migrated."
            } else {
                if (mobileUser != null) {
                    updateDataSet(mobile, "Switched_" + mobile)
                    mobileUser.enabled = false
                    mobileUser.accountLocked = true
                    mobileUser.accountExpired = true
                    mobileUser.passwordExpired = true
                    mobileUser.username = "Switched_" + mobile
                    mobileUser.save(failOnError: true, flush: true)
                }

                user = User.findByUsername(username)

                //if user exists and not yet switched
                if (user != null && (user.oldUsername == null || user.oldUsername == "")) {
                    user.oldUsername = user.username
                    user.username = mobile
                    user.mobile = mobile
                    user.otpFinished = "true"
                    if (mobileUser != null) user.password = mobileUser.password
                    user.save(failOnError: true, flush: true)
                    updateDataSet(user.oldUsername, mobile)
                    statusStr = "User switched"
                    dataProviderService.getBooksListForUser(user.username)
                } else if (user == null) {
                    statusStr = "User is already migrated."
                }
            }
        } else {
            statusStr="Mobile number not valid"
        }

        json = ['status':statusStr=='User switched'?'OK':'Fail', 'condition':statusStr]
        render  json as JSON
    }

    def migrateGoogleUserWithMerge(User googleUser) {
        String statusStr="User "
        User normalUser = User.findByEmail(googleUser.email)

        if (normalUser != null && normalUser.email==normalUser.username) {
            normalUser.enabled = false
            normalUser.accountLocked = true
            normalUser.accountExpired = true
            normalUser.passwordExpired = true
            normalUser.username = "Merged_$normalUser.username"
            normalUser.save(failOnError: true, flush: true)
            statusStr += "merged, "
        }

        //if user exists and not yet merged
        if (googleUser!=null && (googleUser.oldUsername==null || googleUser.oldUsername=="")) {
            googleUser.oldUsername = googleUser.username
            googleUser.username = googleUser.email
            googleUser.otpFinished = "true"
            googleUser.save(failOnError: true, flush: true)
            updateDataSet(googleUser.oldUsername, googleUser.username)
            statusStr += "migrated"
            dataProviderService.getBooksListForUser(googleUser.username)
        } else {
            statusStr += "already migrated"
        }

        return ['status':statusStr!='User already migrated'?'OK':'Fail', 'condition':statusStr]
    }

    def sendRecomentBook(){
        String userEmail = ""
        if(springSecurityService.currentUser){
            User user = User.findByUsername(springSecurityService.currentUser.username)
            if(user.email!="" && user.email!=null) {
                userEmail = user.email
                if(userManagementService.validateEmail(user.email,(Integer)session["siteId"])) {
                    userManagementService.sendRecommendBookEmailEvidyaUser(user.email, user.name, params.title)
                }
            }
        }
        if(userManagementService.validateEmail(params.toEmail,(Integer)session["siteId"])) {
            String coverImage = request.getScheme() +"://" + request.getServerName() + "/funlearn/showProfileImage?id=" + params.id + "&fileName=" + params.coverImage + "&type=books&imgType=passport"
            userManagementService.sendRecommendBookEmail(coverImage, params.siteName, params.toEmail,params.title,params.id,params.author,userEmail)
        }

        return
    }

    def etextsSuggestbookemail(){
        String userEmail = ""
        if(springSecurityService.currentUser){
            User user = User.findByUsername(springSecurityService.currentUser.username)
            if(user.email!="" && user.email!=null) {
                if(userManagementService.validateEmail(user.email,(Integer)session["siteId"])) {
                    userEmail = user.email
                    userManagementService.sendRecomentBookEtextsUser(user.email, user.name,params.title)
                }
            }
        }
        if(userManagementService.validateEmail(params.toEmail,(Integer)session["siteId"])) {
            String coverImage = request.getScheme() +"://" + request.getServerName() + "/funlearn/showProfileImage?id=" + params.id + "&fileName=" + params.coverImage + "&type=books&imgType=passport"
            userManagementService.sendRecomentBookEtexts(coverImage, params.siteName, params.toEmail,params.title,params.id,params.author,userEmail)
        }

        return
    }

    def sendRecomentBookEbouquet(){
        if(userManagementService.validateEmail(params.toEmail,24)) {
            String userEmail = ""
            if(springSecurityService.currentUser){
                User user = User.findByUsername(springSecurityService.currentUser.username)
                if(user.email!="" && user.email!=null) {
                    userEmail = user.email
                    userManagementService.sendRecommendBookEbouquetUser(user.email, user.name, params.title)
                }
            }
            String coverImage = request.getScheme() +"://" + request.getServerName() + "/funlearn/showProfileImage?id=" + params.id + "&fileName=" + params.coverImage + "&type=books&imgType=passport"
            userManagementService.sendRecomentBookEbouquet(coverImage, params.siteName, params.toEmail,params.title,params.id,params.author,userEmail)
        }
        return
    }

    def sendRecomentBookWk(){
        if(userManagementService.validateEmail(params.toEmail,(Integer)session["siteId"])) {
            String coverImage = request.getScheme() +"://" + request.getServerName() + "/funlearn/showProfileImage?id=" + params.id + "&fileName=" + params.coverImage + "&type=books&imgType=passport"
            userManagementService.sendRecomentBookEmailWk(coverImage, params.siteName, params.toEmail,params.title,params.id,params.author)
        }

        return
    }

    def evidyaSuggestbookemail(){}

    def wolterskluwerSuggestbookemail(){}

    @Transactional
    def userExistsCheck(){
        println("oko")
        boolean json=false
        String username
        String appCode=null
        Long siteId
        String tempUserName

        if (request.getHeader('Content-Type')!=null&&request.getHeader('Content-Type').startsWith("application/json")) {
            println("inside the metht")
            json=true
            def jsonObj = request.JSON
            username=jsonObj.username
            appCode=jsonObj.appCode
            siteId = new Long(jsonObj.siteId)
        } else {
            username=params.username
            appCode=params.appCode
            siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
        }
        def siteName = dataProviderService.getSiteMst(siteId).siteName
        tempUserName = username
        if(!"eutkarsh".equals(siteName)){
            if(!username.startsWith(""+siteId+"_")) tempUserName = ""+siteId+"_"+username
        }
        User user = User.findByUsername(tempUserName)
        if(user==null){
            //send OTP
            AuthenticationOtp.executeUpdate("delete from AuthenticationOtp where contact='"+username+"' and site_id="+siteId)

            Random generator = new Random()
            def otp = generator.nextInt(900000)+100000
            AuthenticationOtp authOtp = new AuthenticationOtp(contact:username, otp:otp+"", siteId: siteId)
            authOtp.save(failOnError: true,flush: true)

            SiteMst sm = dataProviderService.getSiteMst(siteId)
             siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName

            if(username!=null && !username.equals("")) {
                if (username.contains("@")) {
                    def clientName = grailsApplication.config.grails.appServer.default == "eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName

                    try {
                        if(userManagementService.validateEmail(username,(Integer)session["siteId"])) {
                            userManagementService.sendOtpEmail(null, username, (Integer) session["siteId"], otp + "", siteName, clientName)

                            if (json) {
                                def json1 = ['status': 'otpSent']
                                render json1 as JSON
                            } else
                                render("otpSent")
                        }
                    } catch (Exception e) {
                        println "Exception in sending OTP mail " + e.toString()
                    }
                } else {
                    def sms_um, sms_pw, sms_sid, sms_txt_acc
                    sms_txt_acc = sm.smsSenderName
                    def message = "Use ${otp} as one time password (OTP) to activate your ${sms_txt_acc} account.${appCode != null ? "\n" + appCode : ''}"
                    utilService.sendSMS(siteId,message,username)
                    if (json) {
                        def json1 = ['status': 'otpSent']
                        render json1 as JSON
                    } else
                        render("otpSent")
                }
            }
        } else {
            if(json){
                def json1 =['status':'userExists']
                render json1 as JSON
            } else
                render("userExists")
        }
    }

    def smsTest()
    {   def otp = "9w23432we3"
        def appCode=null
        def mobile=params.mobile
        RestBuilder rest = new RestBuilder()
        def message = "Use ${otp} as one time password (OTP) to activate your Utkarsh App account.${appCode != null ? "\n" + appCode : ''}"
        try {
            def resp = rest.post("http://app.smsinsta.in/vendorsms/pushsms.aspx?clientid=325e5c53-aae3-4a27-af42-8795bf4cae6c&apikey=227b28cf-ef17-4764-bc46-9cfe4a56fdfd&msisdn="+mobile+"&sid=UTKRSH&msg="+message+"&fl=0&gwid=2")
            def checkPassed = resp.text.indexOf("Success")>0
        }catch(Exception e){
            println("exception ="+e.toString())
        }
        render "hello"
    }


    def checkUserExistForSite(){
        def status="";
        Integer siteId=Integer.parseInt(params.siteId);
        User user = User.findByUsername(siteId+"_"+params.username);
        if(user==null){
            status="No user"
        }else{
            status="user exist"
        }

        def json=['status':status]
        render json as JSON
    }

    @Transactional
    def checkOTPForProfileEdit() {
        String email, mobile, email_otp, mobile_otp

        boolean checkPassed = true
        String statusStr = "Fail"
        boolean userExists=false;
        boolean allowLogin=true
        User user;
        Integer siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)

        String authenticationTokenString;
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                email=jsonObj.email
                mobile=jsonObj.mobile
                email_otp=jsonObj.email_otp
                mobile_otp=jsonObj.mobile_otp
                siteId = new Long(jsonObj.siteId)
            } else {
                email = params.email
                mobile = params.mobile
                email_otp = params.email_otp
                mobile_otp = params.mobile_otp
            }
        }


        AuthenticationOtp authOtp

        if (mobile!=null && !mobile.equals("")) {
            authOtp = AuthenticationOtp.findByContactAndSiteId(mobile,siteId)
            if(authOtp==null || !mobile_otp.equals(authOtp.otp)) {
                checkPassed = false
                statusStr = "M"+statusStr
            }
        }

        if (checkPassed && email!=null && !email.equals("")) {
            authOtp = AuthenticationOtp.findByContactAndSiteId(email,siteId)
            if(authOtp==null || !email_otp.equals(authOtp.otp)) {
                checkPassed = false
                statusStr = statusStr+"E"
            }
        }

        if(checkPassed) {
            //to the see if the user already exists
            String siteName = dataProviderService.getSiteMst(siteId).siteName
            if (mobile != null && !mobile.equals("")) {
                if("eutkarsh".equals(siteName))
                    user = User.findByUsername(mobile)
                else user = User.findByUsername(""+siteId+"_"+mobile)
            } else {
                if("eutkarsh".equals(siteName))
                    user = User.findByUsername(email)
                else user = User.findByUsername(""+siteId+"_"+email)
                if (user == null) {
                    user = User.findByUsername("Google" + email)
                }
            }
            if (user != null) {
                checkPassed = false
                statusStr = 'username error'
            }
        }

        def json = ['status':checkPassed?"OK":statusStr, 'userExists':userExists,'allowLogin':allowLogin,'token':authenticationTokenString]
        render json as JSON
    }

    def userBookPurchase() {
        [title:"Customer Support - Wonderslate", commonTemplate:"true"]
    }

    @Transactional
    def getAIAUserOrdersList(){
        String sql =   " SELECT bp.id, bm.title, bp.date_created, bm.cover_image, bm.id bookId, bp.expiry_date ,bp.po_no" +
                " FROM books_permission bp,books_mst bm where  bm.id=bp.book_id and" +
                " bp.username='"+params.username+"'" +
                " order by date_created desc"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)

        List orders = results.collect {comp ->
            if(comp.po_no!=null){
                PurchaseOrder purchaseOrder = dataProviderService.getPurchaseOrder(new Long(comp.po_no))
                return [purchaseId: purchaseOrder.sequencePo!=null?"UT"+purchaseOrder.sequencePo:purchaseOrder.id,
                        title: comp.title, currency: purchaseOrder.currency,  amount: purchaseOrder.amount,
                        orderedDate: purchaseOrder.dateCreated,
                        coverImage: comp.cover_image, bookId: comp.bookId, paymentId: purchaseOrder.paymentId,
                        poFor: purchaseOrder.poFor,noOfChapters: "0",
                        expiryDate: comp.expiry_date]
            }else {
                return [purchaseId: comp.id, title: comp.title, currency: "", amount: "Free", orderedDate: comp.date_created,
                        coverImage: comp.cover_image, bookId: comp.bookId, paymentId: "", poFor: "Book", noOfChapters: "0",
                        expiryDate: comp.expiry_date]
            }
        }

        def json =[
                'results': orders,
                'status': orders ? "OK" : "Nothing present"
        ]

        render json as JSON
    }

    def instituteWelcomeEmail(){

    }

    def userActiveCartEmail(){

    }
    def userPaidPreviewEmail(){

    }
    def userPurchasedBookEmail(){

    }


    @Transactional
    def sendCartActiveEmail() {
        String sql = " SELECT DISTINCT" +
                "    (scad.username),scad.site_id siteId,u.email,u.win " +
                " FROM " +
                " reports.shopping_cart_active_dtl scad,reports.user u " +
                " WHERE  scad.username=u.username and u.email is not null and u.email !='' and " +
                "    (DATE(scad.date_created) = CURDATE() - INTERVAL 2 DAY" +
                "        || DATE(scad.date_created) = CURDATE() - INTERVAL 10 DAY)"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        Random generator = new Random()
        String encodedString
        String originalInput
        String random
        results.collect { comp ->
            random = generator.nextInt(900000)+100000
            originalInput=random.substring(0,3)+comp.win+random.substring(3,6)
            encodedString = Base64.getEncoder().encodeToString(originalInput.getBytes());
            try {
                if (userManagementService.validateEmail(comp.email + "", comp.siteId)) {
                    userManagementService.sendCartActiveEmail(comp.email + "", comp.siteId,encodedString)
                }
            } catch (Exception e) {
                println("exception in =sendCartActiveEmail" + e.toString())
            }
        }
    }

    @Transactional
    def sendPreviewEmail(){
        List relatedBooks
        String viewsql = " SELECT " +
                "    distinct  bvd.site_id,bvd.username,u.email,u.win " +
                "FROM " +
                "    reports.books_view_dtl bvd," +
                "    reports.purchase_order po," +
                "    reports.user u," +
                "    reports.books_mst bm " +
                " WHERE    u.email is not null and u.email !='' AND   " +
                "    bvd.username not in (SELECT \n" +
                "   group_concat(distinct(scad.username))\n" +
                "FROM\n" +
                "    reports.shopping_cart_active_dtl scad,\n" +
                "    reports.user u\n" +
                "WHERE\n" +
                "    scad.username = u.username\n" +
                "        AND u.email IS NOT NULL\n" +
                "        AND u.email != ''\n" +
                "        AND (DATE(scad.date_created) = CURDATE() - INTERVAL 2 DAY\n" +
                "        || DATE(scad.date_created) = CURDATE() - INTERVAL 10 DAY)) and bvd.view_type IN ('store','preview')" +
                "    AND  (DATE(bvd.date_created) = CURDATE() - INTERVAL 2 DAY" +
                "                        || DATE(bvd.date_created) = CURDATE() - INTERVAL 10 DAY) " +
                "    AND bm.id=bvd.book_id " +
                "    and ifnull(bm.price,0)!=0 " +
                "    AND u.username = bvd.username " +
                "    and po.item_code=bvd.book_id " +
                "    and po.username!=u.username "

        def viewdataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def viewsql1 = new Sql(viewdataSource)
        def viewresults = viewsql1.rows(viewsql)
        Random generator = new Random()
        String encodedString
        String originalInput
        String random
        viewresults.collect { comp ->
            String relatedSql = "SELECT distinct bm.id bookId,bm.cover_image image,bm.title title  FROM reports.books_mst bm, reports.books_view_dtl bvd WHERE bvd.book_id=bm.id AND \n" +
                    "                   bvd.username  in ('"+comp.username+"') and bvd.view_type IN ('store','preview')\n" +
                    "    AND  (DATE(bvd.date_created) = CURDATE() - INTERVAL 2 DAY\n" +
                    "                        || DATE(bvd.date_created) = CURDATE() - INTERVAL 10 DAY)\n" +
                    "                     group by bm.id,bm.cover_image limit 5"
            def relateddataSource = grailsApplication.mainContext.getBean('dataSource_reports')
            def relatedsql1 = new Sql(relateddataSource)
            def relatedresults = relatedsql1.rows(relatedSql)
            relatedBooks = relatedresults.collect { relatedbooks ->
                return [bookId: relatedbooks.bookId, coverImage: relatedbooks.image, urlTitle: relatedbooks.title.replaceAll(' ', '-'), bookTitle: relatedbooks.title]
            }
            random = generator.nextInt(900000)+100000
            originalInput=random.substring(0,3)+comp.win+random.substring(3,6)
            encodedString = Base64.getEncoder().encodeToString(originalInput.getBytes());
            try {
                if (userManagementService.validateEmail(comp.email + "", comp.site_id)) {
                    userManagementService.sendPreviewedBookEmail(comp.email + "", comp.site_id, relatedBooks,encodedString)
                }
            } catch (Exception e) {
                println("exception in sendPurchasedBookEmail=" + e.toString())
            }
        }

    }

    @Transactional
    def sendPurchasedBookEmail() {
        List relatedBooks
        String sql = "select t2.username, t2.item_code, t2.site_id, t2.grade, t2.level,t2.syllabus,t2.email,t2.win from  " +
                " (SELECT t.username,t.item_code,t.site_id,t.grade,t.level,t.syllabus,t.email,t.win, " +
                "   @order_rank := IF(@current_user = t.username, @order_rank + 1, 1) AS order_rank, " +
                " @current_user := t.username " +
                "  FROM (SELECT po.username,po.item_code,po.site_id,btd.grade,btd.level,btd.syllabus,u.email,u.win " +
                "  FROM reports.purchase_order po,reports.books_tag_dtl btd ,reports.user u " +
                " WHERE po.username=u.username AND po.item_code=btd.book_id AND u.email is not null AND (DATE(po.date_created) = CURDATE() - INTERVAL 2 DAY " +
                "        || DATE(po.date_created) = CURDATE() - INTERVAL 30 DAY) order by po.username) t CROSS JOIN (SELECT @order_rank := 0) t1) t2 " +
                "where t2.order_rank=1;"
        def dataSource = grailsApplication.mainContext.getBean('dataSource_reports')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        Random generator = new Random()
        String encodedString
        String originalInput
        String random
        results.collect { comp ->
            String relatedSql = "SELECT distinct bm.id bookId,bm.cover_image image,bm.title title  FROM reports.books_mst bm, reports.books_tag_dtl btd WHERE btd.book_id=bm.id AND btd.level='" + comp.level + "' and btd.syllabus='" + comp.syllabus + "'" +
                    " and btd.grade='" + comp.grade + "' and bm.site_id='" + comp.site_id + "' and bm.status='published' and ifnull(bm.price,0)!=0 " +
                    " group by bm.id,bm.cover_image order by bm.date_published desc limit 5"
            def relateddataSource = grailsApplication.mainContext.getBean('dataSource_reports')
            def relatedsql1 = new Sql(relateddataSource)
            def relatedresults = relatedsql1.rows(relatedSql)
            relatedBooks = relatedresults.collect { relatedbooks ->
                return [bookId: relatedbooks.bookId, coverImage: relatedbooks.image, urlTitle: relatedbooks.title.replaceAll(' ', '-'), bookTitle: relatedbooks.title]
            }
            random = generator.nextInt(900000)+100000
            originalInput=random.substring(0,3)+comp.win+random.substring(3,6)
            encodedString = Base64.getEncoder().encodeToString(originalInput.getBytes());
            try {
                if (userManagementService.validateEmail(comp.email + "", comp.site_id)) {
                    userManagementService.sendPurchasedBookEmail(comp.email + "", comp.site_id, relatedBooks,encodedString)
                }
            } catch (Exception e) {
                println("exception in sendPurchasedBookEmail=" + e.toString())
            }
        }
    }

    def returnPolicy(){
        [title: 'Return and Cancellation Policy',commonTemplate: 'true']
    }

    def externalPurchaseMail(){

    }

    @Transactional
    def sendQuery(){
      /**  String name = params.name
        String email = params.email
        String mobile = params.mobile
        String company = params.company
        String query = params.query
        String msg = "Name : "+name+"\n"+
                "Email : "+email+"\n"+
                "Mobile : "+mobile+"\n"+
                "Company : "+company+"\n"+
                "Query : "+query

        SiteDtl siteDtl = SiteDtl.findBySiteId(new Long(session['siteId']))
        String toMail = "<EMAIL>, <EMAIL>, <EMAIL>"
        String mailSubject = "Query from Contact us page - "

        if (siteDtl!=null){
            if (siteDtl.emailAddress!=null && siteDtl.emailAddress!=""){
                toMail = siteDtl.emailAddress+","
            }
            if (session['wileySite'] == true || session['wileySite'] == 'true'){
                toMail += ',<EMAIL>'
                mailSubject += 'Wonderslate'
            }else{
                mailSubject += session['clientName']
            }
        }

        try {
            String[] toEmails = toMail.split(',')
            for(int i=0;i<toEmails.length;i++) {
                mailService.sendMail {
                    async true
                    to toEmails[i]
                    from "Wonderslate <<EMAIL>>"
                    subject mailSubject
                    text msg
                }
            }
        } catch (Exception e) {
            println("Exception in sending query email and exception is " + e.toString())
        }*/
        render "mail sent"
    }

    def paymentPendingEmail(){

    }


    @Secured(['ROLE_CUSTOMER_SUPPORT']) @Transactional
    def deleteUserOtp(){
        def json
        try {
            String contact = params.contact
            creationService.deleteOTPForUser(""+contact,params.siteId)
            json = ['status': 'success', 'message': 'OTP deleted successfully']
        } catch (Exception e) {
            log.error("Error deleting OTP: " + e.getMessage(), e)
            json = ['status': 'error', 'message': 'An error occurred while deleting OTP']
        }
        render json as JSON
    }

    @Secured(['ROLE_CUSTOMER_SUPPORT'])
    def otpAdmin(){

    }

    @Transactional
    def getOtpsByContact(){
        String contact = params.contact
        Long siteId = null
        def json

        // Handle both form parameters and JSON requests
        if(request.getHeader('Content-Type')!=null) {
            if (request.getHeader('Content-Type').startsWith("application/json")) {
                def jsonObj = request.JSON
                contact = jsonObj.contact
                siteId = jsonObj.siteId ? new Long(jsonObj.siteId) : null
            } else {
                contact = params.contact
                siteId = params.siteId ? new Long(params.siteId) : null
            }
        }

        try {
            if(contact == null || contact.trim().equals("")) {
                json = ['status': 'error', 'message': 'Contact parameter is required', 'results': []]
                render json as JSON
                return
            }

            if(siteId == null) {
                json = ['status': 'error', 'message': 'Site ID parameter is required', 'results': []]
                render json as JSON
                return
            }

            List<AuthenticationOtp> otpRecords = AuthenticationOtp.findAllByContactAndSiteId(contact, siteId, [sort: "dateCreated", order: "desc"])

            List otpResults = otpRecords.collect { otp ->
                return [
                    id: otp.id,
                    otp: otp.otp,
                    contact: otp.contact,
                    siteId: otp.siteId,
                    dateCreated: otp.dateCreated
                ]
            }

            json = [
                'status': otpResults ? "OK" : "No records found",
                'results': otpResults,
                'count': otpResults.size(),
                'contact': contact,
                'siteId': siteId
            ]

        } catch (Exception e) {
            log.error("Error fetching OTPs: " + e.getMessage(), e)
            json = ['status': 'error', 'message': 'An error occurred while fetching OTPs', 'results': []]
        }

        render json as JSON
    }


}
