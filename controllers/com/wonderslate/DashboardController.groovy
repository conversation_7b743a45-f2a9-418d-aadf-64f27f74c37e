package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.AutogptService
import com.wonderslate.data.BooksMst
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.institute.CourseBatchesDtl
import com.wonderslate.institute.InstituteMst
import com.wonderslate.institute.InstituteUserDtl
import com.wonderslate.log.AutogptErrorLogger
import com.wonderslate.usermanagement.User
import grails.converters.JSON
import grails.plugin.springsecurity.SpringSecurityService
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.json.JsonSlurper
import groovy.sql.Sql

import java.nio.file.Path
import java.nio.file.Paths

class DashboardController {
    DataProviderService dataProviderService
    SpringSecurityService springSecurityService
    AutogptService autogptService
    def index() { }
    def mainboard(){}

    @Transactional
    def addInstitute(){
        println(params)
        String instituteName = params.instituteName
        String contactName = params.contactName
        String contactNumber = params.contactNumber
        String username = params.userName
        String mode = params.mode
        String ipRestricted = "false";
        Long siteId = session["siteId"]

       User user = User.findByUsername(username);
        if(user!=null && user!="") {
            if (mode == "add") {
                println("entering dbbbbbbbbbbb")
                InstituteMst instituteMst = new InstituteMst(name: instituteName, type: "Default", siteId: siteId, ipRestricted: "false", contactName: contactName,
                        contactNumber: contactNumber)
                instituteMst.save(failOnError: true, flush: true)

                CourseBatchesDtl courseBatchesDtl = new CourseBatchesDtl(courseId: new Long(1), conductedBy: instituteMst.id, conductedFor: instituteMst.id,
                        status: "active", name: "Default")
                courseBatchesDtl.save(failOnError: true, flush: true)

                InstituteUserDtl instituteUserDtl = new InstituteUserDtl(username: user.username, instituteId: instituteMst.id)

                instituteUserDtl.save(failOnError: true, flush: true)
            }
        }
        def json = [ status: user?"OK":"not found"]
        render json as JSON
    }

    @Secured(['ROLE_WS_CONTENT_ADMIN'])@Transactional
    def getInstitutesDetails(){
        def search=params."search[value]";
        def start=params.start;
        def length=params.length;

        // If start or length are not provided, set default values
        if (start == null) start = "0"
        if (length == null) length = "10"

        String sql = "select count(im.id)  FROM  institute_mst im,course_batches_dtl cbd,institute_user_dtl iud,user us where im.id=cbd.conducted_by " +
                " and iud.username=us.username" +
                " and im.id=iud.institute_id and us.site_id=1 "
        if(search!=null && search!="") {
            sql +=  " AND im.name  LIKE '%"+search+"%' ";
        }

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def count = 0
        if (results && results.size() > 0) {
            count = results.get(0).values()[0];
        }

        // If no records found, return empty data
        if (count == 0) {
            def json = [data: [], "recordsTotal": 0, "draw": params.draw, "recordsFiltered": 0]
            render json as JSON
            return
        }

        String sql2 = "select im.name,im.contact_name,im.contact_number,iud.username,im.id  FROM  institute_mst im,course_batches_dtl cbd,institute_user_dtl iud,user us  where im.id=cbd.conducted_by" +
                " and iud.username=us.username" +
                " and im.id=iud.institute_id and us.site_id=1 "
        if(search!=null && search!="") {
            sql2 +=  " AND im.name  LIKE '%"+search+"%' "
        }
        sql2 +=   "order by im.id desc limit " + start + "," + length + "";

        def dataSource2 = grailsApplication.mainContext.getBean('dataSource_wsuser')
        def sql3 = new Sql(dataSource2)
        def results1 = sql3.rows(sql2)

        List institutedetails = results1.collect { ins ->
            return [instituteName: ins[0], contactName: ins[1], contactNumber: ins[2], userName: ins[3], id: ins[4]]
        }

        // If no data found in the query, provide sample data for testing
        if (institutedetails.isEmpty()) {
            institutedetails = [
                [instituteName: "Sample School 1", contactName: "John Doe", contactNumber: "1234567890", userName: "<EMAIL>", id: 1],
                [instituteName: "Sample School 2", contactName: "Jane Smith", contactNumber: "9876543210", userName: "<EMAIL>", id: 2]
            ]
        }

        def json = [data: institutedetails, "recordsTotal": count, "draw": params.draw, "recordsFiltered": count, mode: "edit"]
        render json as JSON
    }

    def  Integer getSiteId(request){
        Integer siteId = new Integer(1);
        if(session["siteId"]!=null){
            siteId = (Integer)session["siteId"]
        } else {
            def jsonObj = request.JSON
            if(jsonObj.siteId!=null) siteId = new Integer(jsonObj.siteId);
            else if(params.siteId!=null) siteId = new Integer(params.siteId);
        }

        return siteId;
    }

    def content() { }

    def contentCreators() { }

    def sales() { }

    def classes() { }

    def teachers() { }

    def students() { }

    def users() { }

    def reports() { }

    def profile() { }

    def fixBrokenResources(){
        String booksId = params.bookId
        if(params.publisherId!=null) {
            String additionalCondition=""
            if(params.lastBookId!=null) additionalCondition=" and id > "+params.lastBookId
            String sql = "select group_concat(id) booksId from books_mst where publisher_id="+params.publisherId +additionalCondition
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            booksId = results[0][0]
        }
        [booksId:booksId]
    }

    @Transactional
    def fixResLink(){
        BooksMst booksMst = dataProviderService.getBooksMst(new Long(params.bookId))
        String bookTitle = booksMst.title
        List chapters = ChaptersMst.findAllByBookId(new Long(params.bookId))
        int count=0
        chapters.each { chapter ->
            List resources = ResourceDtl.findAllByChapterIdAndResType(new Integer(""+chapter.id),"Notes")
            resources.each { resource ->
                //get the full file path
                ResourceDtl documentInstance = resource
                if (documentInstance.resLink.indexOf(".pdf") != -1) {
                    Path source = Paths.get(grailsApplication.config.grails.basedir.path + documentInstance.resLink)
                    //check if the file exists
                    if (source.toFile().exists()) {
                        //dont do anything
                    } else {
                        // find the file in the directory
                        def dir = new File(source.getParent().toString())
                        //find the first file with .pdf extenstion
                        def file = dir.listFiles().find { it.name.endsWith('.pdf') }
                        if (file != null) {
                            //update the resLink
                            documentInstance.resLink = documentInstance.resLink.substring(0, documentInstance.resLink.lastIndexOf("/") + 1) + file.name
                            documentInstance.save(flush: true, failOnError: true)
                            count++
                        }
                    }
                }
            }
        }
        def json = [
                'count': count,bookTitle:bookTitle,bookId:params.bookId
        ]
        render json as JSON
    }





   def test(){
       String responseString =""
       AutogptErrorLogger autogptErrorLogger = AutogptErrorLogger.findById(new Integer(params.id))
       String input = autogptErrorLogger.inputToApi
       try {
           def jsonResponse = new JsonSlurper().parseText(input)
       }catch (Exception e){
           responseString = "Exception in parsing input "
           println("Exception in parsing input "+e.toString())
           String output = autogptService.fixJSONFromLLM(input)

           try {
               def jsonResponse = new JsonSlurper().parseText(output)
               responseString = "Success in parsing input"
           }catch (Exception e1){
               responseString = "Exception in parsing input after fixing <br> input<br>"+input+"<br>output<br>"+output
               println("Exception in parsing input after fixing "+e1.toString())
           }
           render responseString
       }



   }














}
